package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.util.Commons;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.dao.mysql.entity.*;
import com.heal.controlcenter.dao.redis.*;
import com.heal.controlcenter.exception.*;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.beans.CompInstanceAttributesBean;
import com.appnomic.appsone.common.util.StringUtils;

import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.security.Security;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Business logic for creating component instances.
 * Handles validation, database operations, and Redis cache updates.
 */
@Slf4j
@Component
public class PostComponentInstanceBL implements BusinessLogic<List<ComponentInstancePojo>, UtilityBean<List<ComponentInstanceBean>>, List<IdPojo>> {

    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final ComponentInstanceDao componentInstanceDao;
    private final CompInstanceDao compInstanceDao;
    private final MasterDataDao masterDataDao;
    private final MasterComponentDao masterComponentDao;
    private final com.heal.controlcenter.dao.mysql.AgentDao agentDao;
    private final EnvironmentDao environmentDao;
    private final ServiceRepo serviceRepo;
    private final InstanceRepo instanceRepo;
    private final ComponentDao componentDao;
    private final BindInDao bindInDao;
    private final TagsDao tagsDao;
    private final AccountRepo accountRepo;
    private final ComponentRepo componentRepo;
    private final AgentRepo agentRepo;

    private static final String MONITOR_PORT ="MonitorPort";

    public PostComponentInstanceBL(ClientValidationUtils clientValidationUtils,
                                   ServerValidationUtils serverValidationUtils,
                                   ComponentInstanceDao componentInstanceDao,
                                   CompInstanceDao compInstanceDao,
                                   MasterDataDao masterDataDao,
                                   MasterComponentDao masterComponentDao,
                                   com.heal.controlcenter.dao.mysql.AgentDao agentDao,
                                   EnvironmentDao environmentDao,
                                   ServiceRepo serviceRepo,
                                   InstanceRepo instanceRepo,
                                   ComponentDao componentDao,
                                   BindInDao bindInDao,
                                   TagsDao tagsDao,
                                   AccountRepo accountRepo,
                                   ComponentRepo componentRepo,
                                   AgentRepo agentRepo) {
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.componentInstanceDao = componentInstanceDao;
        this.compInstanceDao = compInstanceDao;
        this.masterDataDao = masterDataDao;
        this.masterComponentDao = masterComponentDao;
        this.agentDao = agentDao;
        this.environmentDao = environmentDao;
        this.serviceRepo = serviceRepo;
        this.instanceRepo = instanceRepo;
        this.componentDao = componentDao;
        this.bindInDao = bindInDao;
        this.tagsDao = tagsDao;
        this.accountRepo = accountRepo;
        this.componentRepo = componentRepo;
        this.agentRepo = agentRepo;
    }

    /**
     * Validates the client-side input for creating component instances.
     * @return UtilityBean containing validated request data
     * @throws ClientException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ComponentInstancePojo>> clientValidation(Object... arguments) throws ClientException {
        List<ComponentInstancePojo> requestBody = (List<ComponentInstancePojo>) arguments[0];
        String accountIdentifier = (String) arguments[1];
        
        // Validate account identifier
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);
        
        // Validate request body
        if (requestBody == null || requestBody.isEmpty()) {
            throw new ClientException("Component instance request list cannot be null or empty");
        }

        // Validate each component instance request
        Map<String, String> errors = new HashMap<>();
        for (int i = 0; i < requestBody.size(); i++) {
            ComponentInstancePojo request = requestBody.get(i);
            request.validate();
            if (!request.getError().isEmpty()) {
                int finalI = i;
                request.getError().forEach((key, value) ->
                    errors.put("componentInstances[" + finalI + "]." + key, value));
            }
        }

        if (!errors.isEmpty()) {
            String errorMessage = errors.toString();
            log.error("Component instance validation failed: {}", errorMessage);
            throw new ClientException(errorMessage);
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);
        log.debug("[clientValidation] Validation successful for accountIdentifier: {}", accountIdentifier);

        BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

        return UtilityBean.<List<ComponentInstancePojo>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .metadata(new HashMap<>())
                .basicUserDetails(basicUserDetails)
                .build();
    }

    /**
     * Validates server-side constraints and prepares data for processing.
     * @param utilityBean Validated client data
     * @return UtilityBean with ComponentInstanceBean objects ready for processing
     * @throws ServerException if server validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ComponentInstanceBean>> serverValidation(UtilityBean<List<ComponentInstancePojo>> utilityBean) throws ServerException {
        try {
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            List<ComponentInstancePojo> requests = utilityBean.getPojoObject();

            // Validate account exists
            Account account = serverValidationUtils.accountValidation(accountIdentifier);
            if (account == null) {
                throw new ServerException("Account not found with identifier: " + accountIdentifier);
            }

            // Get user ID from metadata
            String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);

            // Convert requests to beans with comprehensive server validations
            List<ComponentInstanceBean> componentInstanceBeans = addServerValidations(requests, userId, account);

            utilityBean.getMetadata().put(Constants.ACCOUNT, account);
            
            return UtilityBean.<List<ComponentInstanceBean>>builder()
                    .requestParams(utilityBean.getRequestParams())
                    .pojoObject(componentInstanceBeans)
                    .metadata(utilityBean.getMetadata())
                    .build();

        } catch (Exception e) {
            log.error("Server validation failed: {}", e.getMessage(), e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }
    }

    /**
     * Processes the component instance creation .
     * This method handles both database insertion and Redis cache updates.
     * @param utilityBean Validated component instance data
     * @return List of created component instance IDs
     * @throws DataProcessingException if processing fails
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogExecutionAnnotation
    public List<IdPojo> process(UtilityBean<List<ComponentInstanceBean>> utilityBean) throws DataProcessingException {
        try {
            List<ComponentInstanceBean> componentInstances = utilityBean.getPojoObject();
            Account account = (Account) utilityBean.getMetadata().get(Constants.ACCOUNT);

            // Process component instances - equivalent to ComponentInstanceBL.process
            return processComponentInstances(componentInstances, account.getIdentifier());

        } catch (Exception e) {
            log.error("Error occurred while processing component instances: {}", e.getMessage(), e);
            throw new DataProcessingException("Error occurred while processing component instances: " + e.getMessage());
        }
    }

    /**
     * Processes component instances
     * Add instances to Redis
     * @param beanList List of validated component instance beans
     * @param accountIdentifier Account identifier
     * @return List of created instance IDs
     * @throws Exception if processing fails
     */
    private List<IdPojo> processComponentInstances(List<ComponentInstanceBean> beanList, String accountIdentifier) throws Exception {

        // Process each component instance in transaction (equivalent to dbi.inTransaction)
        for (ComponentInstanceBean componentInstanceBean : beanList) {
            addComponentInstance(componentInstanceBean);
        }

        // Add instances to Redis - equivalent to ComponentInstanceUtil.addInstancesToRedis
        return addInstancesToRedis(beanList, accountIdentifier);
    }

    /**
     * Adds component instance with KPIs.
     * @param bean Component instance bean
     * @throws Exception if insertion fails
     */
    private void addComponentInstance(ComponentInstanceBean bean) throws Exception {
        int instanceId = addComponentInstanceToDatabase(bean);
        if (instanceId != -1) {
            if (bean.getIsUpdate() == 0) {
                addConfigWatchKPIs(bean, instanceId);
            } else {
                log.info("Updated comp instance for identifier:{}, name :{}", bean.getIdentifier(), bean.getName());
            }
        }
    }

    /**
     * Adds config watch KPIs for component instance - equivalent to addConfigWatchKPIs in original.
     * Converted from JDBI to JDBC pattern following appsone-controlcenter repository.
     * @param bean Component instance bean
     * @param instanceId Instance ID
     * @throws HealControlCenterException if KPI addition fails
     */
    private void addConfigWatchKPIs(ComponentInstanceBean bean, int instanceId) throws HealControlCenterException {
        try {
            List<CompInstanceKpiGroupDetailsBean> kpiList = componentDao.getConfigWatchFilesByComponentId(bean.getMstComponentId(), bean.getMstComponentVersionId());
            if (kpiList != null && !kpiList.isEmpty()) {
                for (CompInstanceKpiGroupDetailsBean kpiBean : kpiList) {
                    kpiBean.setCompInstanceId(instanceId);
                    kpiBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    kpiBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    kpiBean.setUserDetailsId(bean.getUserDetailsId());
                    kpiBean.setStatus(1);
                    kpiBean.setAliasName(kpiBean.getAttributeValue() == null ? Constants.ALL : kpiBean.getAttributeValue());

                    // Use JDBC-based approach instead of JDBI handle
                    int id = addGroupComponentInstanceKPI(kpiBean);
                    if (id == -1) {
                        String err = "Unable to add Group KPIs -" + kpiBean.getMstKpiDetailsId() + " for component instance id-" + instanceId;
                        log.error(err);
                        throw new HealControlCenterException(err);
                    }
                    log.info("Added config/file watch KPIs: {}, attribute: {}, for component instance id: {}", kpiBean.getMstKpiDetailsId(),
                            kpiBean.getAttributeValue(), instanceId);
                }
            } else {
                log.info("No config/file watch KPIs found for component instance id: {}, component id:{}, component version id: {}",
                        instanceId, bean.getMstComponentId(), bean.getMstComponentVersionId());
            }
        } catch (HealControlCenterException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error adding config watch KPIs for component instance id: {}", instanceId, e);
            throw new HealControlCenterException("Error adding config watch KPIs for component instance id: " + instanceId);
        }
    }

    /**
     * Adds a single group component instance KPI using JDBC instead of JDBI.
     * Equivalent to CompInstanceDataService.addGroupComponentInstanceKPI from appsone-controlcenter.
     * This method follows the JDBC pattern used in appsone-controlcenter for returning generated IDs.
     * @param kpiBean The KPI bean to add
     * @return The generated ID from the database, or -1 if failed
     */
    private int addGroupComponentInstanceKPI(CompInstanceKpiGroupDetailsBean kpiBean) {
        try {
            if (kpiBean.getAliasName() == null || kpiBean.getAliasName().trim().isEmpty()) {
                kpiBean.setAliasName(kpiBean.getAttributeValue());
            }
            // Use the new JDBC method that returns generated ID
            return compInstanceDao.addSingleGroupCompInstanceKpiDetail(kpiBean);
        } catch (Exception e) {
            log.error("Error occurred while adding group component instance KPI for component instance id: {}. Details: {}",
                    kpiBean.getCompInstanceId(), e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Comprehensive server validations following the original appsone-controlcenter pattern.
     * This method performs all the validations that were done in ComponentInstanceBL.addServerValidations
     * and ComponentInstanceUtil.validateAndGetComponentInstance.
     */
    private List<ComponentInstanceBean> addServerValidations(List<ComponentInstancePojo> instances, String userId, Account account) throws HealControlCenterException {
        try {
            List<ComponentInstanceBean> beanList = new ArrayList<>();

            // Get service list for validation (equivalent to CommonUtils.getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, accId))
            List<Controller> serviceList = getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, account.getId());

            if (instances != null) {
                for (ComponentInstancePojo instance : instances) {
                    ComponentInstanceBean bean = validateAndGetComponentInstance(instance, userId, account.getId(), serviceList, new ArrayList<>());
                    beanList.add(bean);
                }
            }

            if (!beanList.isEmpty()) {
                validateUniquenessInBatch(beanList);
            }

            return beanList;
        } catch (Exception e) {
            log.error("Error in server validations: {}", e.getMessage(), e);
            throw new HealControlCenterException("Server validation failed: " + e.getMessage());
        }
    }

    private void validateUniquenessInBatch(List<ComponentInstanceBean> beans) throws HealControlCenterException {
        List<ComponentInstanceBean> hostList = beans.stream().filter(b -> b.getIsHost() == 1).collect(Collectors.toList());
        List<ComponentInstanceBean> componentInstanceList = beans.stream().filter(b -> b.getIsHost() == 0).collect(Collectors.toList());

        validateUniquenessForInstanceType(hostList, true);
        validateUniquenessForInstanceType(componentInstanceList, false);
    }

    private void validateUniquenessForInstanceType(List<ComponentInstanceBean> instanceList, boolean isHost) throws HealControlCenterException {
        for (int i = 0; i < instanceList.size() - 1; i++) {
            ComponentInstanceBean instance1 = instanceList.get(i);
            List<Integer> list1 = Arrays.stream(instance1.getServiceIds()).boxed().sorted().collect(Collectors.toList());

            String monitorPort1 = isHost ? null : getMonitorPort(instance1);

            for (int j = i + 1; j < instanceList.size(); j++) {
                ComponentInstanceBean instance2 = instanceList.get(j);

                if (isHost) {
                    if (instance1.getHostAddress().equals(instance2.getHostAddress())) {
                        throw new HealControlCenterException("Multiple Host Instance with same host address found in request Body.");
                    }
                } else {
                    String monitorPort2 = getMonitorPort(instance2);
                    if (monitorPort1 != null && monitorPort2 != null &&
                            (instance1.getHostAddress() + ":" + monitorPort1).equals(instance2.getHostAddress() + ":" + monitorPort2)) {
                        throw new HealControlCenterException("Multiple component Instance with same HostAddress:MonitorPort pair found in request Body.");
                    }
                }

                List<Integer> list2 = Arrays.stream(instance2.getServiceIds()).boxed().sorted().collect(Collectors.toList());
                if (equateList(list1, list2)) {
                    String instanceType = isHost ? "Host" : "Component";
                    if (instance1.getMstComponentId() != instance2.getMstComponentId()) {
                        throw new HealControlCenterException("Multiple " + instanceType + " Instance with different component name mapped to same service found in request Body.");
                    } else if (instance1.getMstCommonVersionId() != instance2.getMstCommonVersionId()) {
                        throw new HealControlCenterException("Multiple " + instanceType + " Instance with different common version mapped to same service found in request Body.");
                    }
                }
            }
        }
    }

    private String getMonitorPort(ComponentInstanceBean bean) {
        return bean.getAttributes().stream()
                .filter(attribute -> attribute.getAttributeName().equalsIgnoreCase(MONITOR_PORT))
                .map(CompInstanceAttributesBean::getAttributeValue)
                .findFirst()
                .orElse(null);
    }

    /**
     * Validates and converts a single ComponentInstancePojo to ComponentInstanceBean.
     * Exact equivalent of ComponentInstanceUtil.validateAndGetComponentInstance from appsone-controlcenter.
     * Converted from JDBI to JDBC.
     */
    private ComponentInstanceBean validateAndGetComponentInstance(ComponentInstancePojo instance, String userId, int accountId,
                                                                 List<Controller> serviceList, List<Integer> agentIdsList) throws HealControlCenterException {
        MasterComponentBean componentBean = getComponentDetails(instance.getComponentName(), instance.getComponentVersion(), accountId);
        int isHost = 0;
        int isPod = 0;
        int hostComponentTypeId = 0;

        MasterComponentTypeBean hostComponentType = masterComponentDao.getMasterComponentTypeUsingName("Host", String.valueOf(accountId));
        if (hostComponentType == null) {
            throw new HealControlCenterException("Component with type 'Host' doesn't exist.");
        }
        hostComponentTypeId = hostComponentType.getId();

        if (hostComponentType.getId() == componentBean.getComponentTypeId()) {
            isHost = 1;
        } else {
            MasterComponentTypeBean podComponentType = masterComponentDao.getMasterComponentTypeUsingName("Pod", String.valueOf(accountId));
            if (podComponentType != null && podComponentType.getId() == componentBean.getComponentTypeId()) {
                isPod = 1;
            }
        }

        int[] serviceIds = getServiceIds(serviceList, instance.getServiceIdentifiers());
        Map<Integer, String> agentIdsMap = getAgentIdsMap(instance.getAgentIdentifiers());

        String name = (instance.getName() == null) ? instance.getIdentifier() : instance.getName();
        Attributes hostAttribute = getAttribute(instance, "HostAddress");
        if (hostAttribute == null) {
            throw new HealControlCenterException("HostAddress attribute is missing for component instance name '" + name + "'");
        }

        int isDR = validateAndGetEnvironment(instance.getEnvironment());

        performUniquenessValidations(isHost, accountId, hostAttribute.getValue(), isDR, instance, componentBean, hostComponentTypeId, serviceIds);

        return buildComponentInstanceBean(instance, userId, accountId, componentBean, isHost, isPod, serviceIds, agentIdsMap, name, hostAttribute, isDR);
    }

    private MasterComponentBean getComponentDetails(String name, String version, int accountId) throws HealControlCenterException {
        MasterComponentBean componentBean = masterComponentDao.getComponentDetailsWithNameAndVersion(name, version, accountId);
        if (componentBean == null) {
            throw new HealControlCenterException("Component with name '" + name + "' and version '" + version + "' doesn't exist.");
        }
        return componentBean;
    }

    private Map<Integer, String> getAgentIdsMap(List<String> agentIdentifiers) throws HealControlCenterException {
        Map<Integer, String> agentIdsMap = new HashMap<>();
        if (agentIdentifiers != null && !agentIdentifiers.isEmpty()) {
            for (String agentIdentifier : agentIdentifiers) {
                AgentBean agentBean = agentDao.getAgentBeanData(agentIdentifier);
                if (agentBean == null) {
                    throw new HealControlCenterException("Agent Identifier '" + agentIdentifier + "' does not exist");
                }
                agentIdsMap.put(agentBean.getId(), agentIdentifier);
            }
        }
        return agentIdsMap;
    }

    private Attributes getAttribute(ComponentInstancePojo instance, String attributeName) {
        return instance.getAttributes().stream()
                .filter(a -> a.getName().equalsIgnoreCase(attributeName))
                .findAny()
                .orElse(null);
    }

    private int validateAndGetEnvironment(String environmentName) throws HealControlCenterException {
        int environmentTypeId = environmentDao.getEnvTypeIdFromTypeName("Environment");
        if (environmentTypeId == 0) {
            throw new HealControlCenterException("Could not find the type id for the type name Environment in Data source.");
        }
        List<ObjPojo> envSubTypeDetails = environmentDao.getEnvSubTypeDetails(environmentTypeId);

        Optional<ObjPojo> envTypeOptional = envSubTypeDetails.stream()
                .filter(f -> f.getName().equalsIgnoreCase(environmentName))
                .findAny();

        if (envTypeOptional.isPresent()) {
            return envTypeOptional.get().getId();
        }

        log.warn("The environment detail provided in the input {} is not among the supported subtypes. Marking environment value as 'NONE'.", environmentName);

        return envSubTypeDetails.stream()
                .filter(f -> f.getName().equalsIgnoreCase("NONE"))
                .findAny()
                .map(ObjPojo::getId)
                .orElseThrow(() -> new HealControlCenterException("NONE subtype not found for Environment."));
    }

    private void performUniquenessValidations(int isHost, int accountId, String hostAddress, int isDR,
                                              ComponentInstancePojo instance, MasterComponentBean componentBean,
                                              int hostComponentTypeId, int[] serviceIds) throws HealControlCenterException {
        if (isHost == 1) {
            validateHostUniqueness(accountId, hostAddress, isDR);
        } else {
            Attributes monitorPortAttribute = getAttribute(instance, "MonitorPort");
            validateComponentInstanceUniqueness(accountId, hostAddress, monitorPortAttribute);
            validateHostExistenceForComponent(accountId, hostAddress);
        }
        validateClusterComponentMatch(serviceIds, accountId, isHost, componentBean, hostComponentTypeId);
    }

    private void validateHostUniqueness(int accountId, String hostAddress, int isDR) throws HealControlCenterException {
        int instanceCount = componentInstanceDao.getInstanceDetailsByHostAddress(accountId, hostAddress, isDR);
        if (instanceCount == -1) {
            throw new HealControlCenterException("Exception while getting count of instances with host_address " + hostAddress);
        }
        if (instanceCount > 0) {
            throw new HealControlCenterException("Combination of host address and environment already exist for account.");
        }
    }

    private void validateComponentInstanceUniqueness(int accountId, String hostAddress, Attributes monitorPortAttribute) throws HealControlCenterException {
        if (monitorPortAttribute != null && !StringUtils.isEmpty(monitorPortAttribute.getValue())) {
            int hostPortExistCount = componentInstanceDao.checkHostAddressMonitorPortExistance(accountId, hostAddress, monitorPortAttribute.getValue());
            if (hostPortExistCount > 0) {
                throw new HealControlCenterException("Component Instance's host address and monitor port already exists in account.");
            }
            if (hostPortExistCount == -1) {
                throw new HealControlCenterException("Exception while checking for host_address and port existence.");
            }
        }
    }

    private void validateHostExistenceForComponent(int accountId, String hostAddress) throws HealControlCenterException {
        if (componentInstanceDao.checkHostAddressExistance(accountId, hostAddress) == 0) {
            throw new HealControlCenterException("Host address does not exist for the component instance.");
        }
    }

    private void validateClusterComponentMatch(int[] serviceIds, int accountId, int isHost, MasterComponentBean componentBean, int hostComponentTypeId) throws HealControlCenterException {
        if (serviceIds == null) return;
        for (int serviceId : serviceIds) {
            List<InstanceClusterServicePojo> instanceDetails = componentInstanceDao.getInstanceClusterServiceDetails(serviceId, accountId);
            if (instanceDetails != null && !instanceDetails.isEmpty()) {
                for (InstanceClusterServicePojo detail : instanceDetails) {
                    boolean isHostInstanceInCluster = detail.getClusterComponentTypeId() == hostComponentTypeId;
                    if ((isHost == 1 && isHostInstanceInCluster) || (isHost == 0 && !isHostInstanceInCluster)) {
                        if (detail.getClusterComponentId() != componentBean.getId()) {
                            throw new HealControlCenterException("Instance's component is different from existing cluster's component for the service id " + serviceId);
                        }
                        if (detail.getClusterCommonVersionId() != componentBean.getCommonVersionId()) {
                            throw new HealControlCenterException("Instance's component common version is different from existing cluster's component common version for the service id " + serviceId);
                        }
                    }
                }
            }
        }
    }

    private ComponentInstanceBean buildComponentInstanceBean(ComponentInstancePojo instance, String userId, int accountId,
                                                             MasterComponentBean componentBean, int isHost, int isPod,
                                                             int[] serviceIds, Map<Integer, String> agentIdsMap, String name,
                                                             Attributes hostAttribute, int isDR) throws HealControlCenterException {
        return ComponentInstanceBean.builder()
                .name(name)
                .identifier(instance.getIdentifier())
                .isDR(isDR)
                .mstComponentId(componentBean.getId())
                .mstComponentName(instance.getComponentName())
                .mstComponentVersionId(componentBean.getComponentVersionId())
                .mstComponentVersion(componentBean.getComponentVersionName())
                .mstComponentTypeId(componentBean.getComponentTypeId())
                .mstComponentType(componentBean.getComponentTypeName())
                .mstCommonVersionId(componentBean.getCommonVersionId())
                .mstCommonVersionName(componentBean.getCommonVersionName())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .userDetailsId(userId)
                .hostAddress(hostAttribute.getValue())
                .accountId(accountId)
                .discovery(instance.getDiscovery())
                .serviceIds(serviceIds)
                .serviceIdentifiers(instance.getServiceIdentifiers())
                .parentIdentifier(instance.getParentIdentifier())
                .parentName(instance.getParentName())
                .isHost(isHost)
                .status(1)
                .agentIdsMap(agentIdsMap)
                .agentIdentifiers(instance.getAgentIdentifiers())
                .isPod(isPod)
                .attributes(getComponentAttributesListBean(instance, componentBean.getId(), componentBean.getCommonVersionId(), userId))
                .supervisorId(instance.getSupervisorId())
                .build();
    }

    /**
     * Gets controllers by type bypassing cache - exact equivalent of CommonUtils.getControllersByTypeBypassCache.
     * Uses DAO layer for data access.
     *
     * @param serviceType The parameter identifies the type of controller (e.g., "Services")
     * @param accountId The parameter identifies the Id of any account
     * @return List of controller, it can be application or services
     */
    private List<Controller> getControllersByTypeBypassCache(String serviceType, int accountId) {
        List<Controller> filtratedControllerList = new ArrayList<>();
        try {
            // Get the service mst subtype (equivalent to MasterCache.getMstTypeForSubTypeName)
            ViewTypesBean subTypeBean = masterComponentDao.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, serviceType);

            // Get all controllers for accountId bypassing cache (equivalent to MasterDataService.getControllerList)
            List<Controller> controllerList = masterComponentDao.getControllerList(accountId);

            // Filter with controller_type_id
            if (subTypeBean != null) {
                filtratedControllerList = controllerList.stream()
                        .filter(t -> t.getControllerTypeId() == subTypeBean.getSubTypeId())
                        .collect(Collectors.toList());
            }

        } catch (Exception e) {
            log.error("Error occurred while fetching controller details for service name: {} account id: {}", serviceType, accountId, e);
        }
        return filtratedControllerList;
    }

    /**
     * Gets service IDs from service list - exact equivalent of ComponentInstanceUtil.getServiceIds.
     * Converted from JDBI to JDBC.
     */
    private int[] getServiceIds(List<Controller> serviceList, List<String> serviceIdentifiers) throws HealControlCenterException {
        int[] serviceIds = new int[serviceIdentifiers.size()];
        String err;

        if (serviceList != null && !serviceIdentifiers.isEmpty()) {
            int i = 0;
            for (String svcIdentifier : serviceIdentifiers) {
                Controller service = serviceList.stream()
                        .filter(c -> (c.getIdentifier().equals(svcIdentifier.trim())) && c.getStatus() == 1)
                        .findAny().orElse(null);
                if (service == null) {
                    err = "Service Identifier '" + svcIdentifier + "' does not exist";
                    log.error(err);
                    throw new HealControlCenterException(err);
                }
                serviceIds[i++] = Integer.parseInt(service.getAppId());
            }
        }
        return serviceIds;
    }

    /**
     * Gets component attributes list bean (equivalent to getComponentAttributesListBean in original).
     */
    private List<CompInstanceAttributesBean> getComponentAttributesListBean(
            ComponentInstancePojo instance, int mstComponentId, int mstCommonVersionId, String userId) throws HealControlCenterException {

        List<CompInstanceAttributesBean> attributesBeanList = new ArrayList<>();
        List<AttributesViewBean> attributesViewBeanList = componentInstanceDao.getAttributeViewDataByComponentAndCommonVersion(mstComponentId, mstCommonVersionId);
        if (instance.getAttributes() != null) {
            String err;
            for (Attributes attribute : instance.getAttributes()) {
                if (attribute.getName().isEmpty()) {
                    err = "Attribute name is empty for component instance ' " + instance.getIdentifier() + " '";
                    log.error(err);
                    throw new HealControlCenterException(err);
                }
                //no need to check for empty value, as empty values will have an entry in db

                AttributesViewBean attributesViewBean = getValidAttribute(attributesViewBeanList, attribute.getName());

                if (attributesViewBean.getAttributeType().equalsIgnoreCase("password")
                        && !StringUtils.isEmpty(attribute.getValue())) {
                    String attributeValue = attribute.getValue();
                    try {
                        attributeValue = new AECSBouncyCastleUtil().decrypt(attributeValue);
                    } catch (Exception e) {
                        err = "Password is not properly encrypted.";
                        log.error(err + " Details: {}", e.getMessage(), e);
                        throw new HealControlCenterException(err);
                    }
                    try {
                        Security.removeProvider(Constants.BC_PROVIDER_NAME);
                        attributeValue = Commons.encrypt(attributeValue);
                        attribute.setValue(attributeValue);
                    } catch (Exception e) {
                        log.error("Error while encrypting. Details: {}", e.getMessage(), e);
                        throw new HealControlCenterException("Error while encrypting");
                    }
                }

                CompInstanceAttributesBean instanceAttributesBean = CompInstanceAttributesBean.builder()
                        .attributeName(attributesViewBean.getAttributeName())
                        .attributeValue(attribute.getValue())
                        .mstComponentAttributeMappingId(attributesViewBean.getMstComponentAttributeMappingId())
                        .mstCommonAttributesId(attributesViewBean.getAttributeId())
                        .userDetailsId(userId)
                        .build();
                attributesBeanList.add(instanceAttributesBean);
            }
        }
        return attributesBeanList;
    }

    private AttributesViewBean getValidAttribute(List<AttributesViewBean> attributesViewBeanList, String name) throws HealControlCenterException {
        if (attributesViewBeanList != null && !attributesViewBeanList.isEmpty()) {
            AttributesViewBean bean = attributesViewBeanList.stream().filter(a -> (a.getAttributeName().equals(name.trim()))).findAny().orElse(null);
            if (bean != null) {
                return bean;
            }
        }

        String err = "Attribute '" + name + "' " + Constants.DOES_NOT_EXIST;
        log.error(err);
        throw new HealControlCenterException(err);
    }

    /**
     * Compares two lists for equality or overlap (equivalent to equateList in original).
     */
    private boolean equateList(List<Integer> list1, List<Integer> list2) {
        List<Integer> list3 = new ArrayList<>(list2);
        list3.retainAll(list1);

        if (list3.size() == list1.size()) {
            return true;
        } else {
            return !list3.isEmpty();
        }
    }

    /**
     * Adds component instance to database - equivalent to ComponentInstanceUtil.addComponentInstance.
     * This method follows the exact pattern from appsone-controlcenter using JDBC operations.
     * @param bean Component instance bean
     * @return Instance ID
     * @throws Exception if insertion fails
     */
    private int addComponentInstanceToDatabase(ComponentInstanceBean bean) throws Exception {
        final int controllerId = masterDataDao.getTagDetails(Constants.CONTROLLER_TAG, 1).getId();
        moreServerValidations(bean);

        int hostId = (bean.getIsHost() == 0) ? getHostId(bean.getHostAddress(), bean.getAccountId(), bean.getIsDR()) : 0;
        bean.setHostId(hostId);

        int clusterId = getOrCreateCluster(bean, controllerId);

        setHostInfo(bean, false);
        bean.setIsCluster(0);

        int instanceId = (bean.getIsUpdate() == 0)
                ? componentInstanceDao.addComponentInstance(bean)
                : componentInstanceDao.updateComponentInstance(bean);

        if (instanceId == -1) {
            throw new HealControlCenterException("Failed to add/update component instance data for name: " + bean.getName());
        }

        bean.setId(instanceId);

        if (bean.getIsUpdate() == 0) {
            log.info("Added component instance for identifier: {}, name: {}", bean.getIdentifier(), bean.getName());
            addClusterMapping(bean, clusterId);
            addAttributes(bean, false);
            addKPIs(bean, instanceId);
            addGroupKPIs(bean, instanceId, false);
            if (!bean.getAgentIdsMap().isEmpty()) {
                addAgentMapping(instanceId, bean.getAgentIdsMap());
            }
        } else {
            log.info("Updated component instance for identifier: {}, name: {}", bean.getIdentifier(), bean.getName());
        }

        return instanceId;
    }

    private int getHostId(String hostAddress, int accountId, int isDR) throws HealControlCenterException {
        try {
            List<HostInstanceDetails> hostInstances = bindInDao.getHostInstanceId(hostAddress, accountId, isDR);
            if (hostInstances.size() > 1) {
                throw new HealControlCenterException("Multiple host instances found for hostAddress " + hostAddress);
            }
            return hostInstances.get(0).getHostInstanceId();
        } catch (HealControlCenterException e) {
            throw new HealControlCenterException("Unable to fetch hostId for component instance. " + e.getMessage());
        }
    }

    private int getOrCreateCluster(ComponentInstanceBean bean, int controllerId) throws HealControlCenterException {
        int clusterId = -1;
        for (int serviceId : bean.getServiceIds()) {
            if (bean.getIsKubernetes() == 1 && bean.getIsHost() == 0 && bean.getIsPod() == 0) {
                CompInstanceAttributesBean attribute = bean.getAttributes().stream()
                        .filter(a -> a.getAttributeName().equals(Constants.ATTRIBUTE_CONTAINER_NAME))
                        .findAny().orElse(null);
                if (attribute == null) {
                    throw new HealControlCenterException(Constants.ATTRIBUTE_CONTAINER_NAME + " attribute is missing for container instance '" + bean.getName() + "'");
                }
                clusterId = componentInstanceDao.getComponentInstanceIdByAttributeServiceCluster(bean.getMstComponentTypeId(), attribute.getAttributeName(), attribute.getAttributeValue(), serviceId, controllerId, bean.getAccountId());
            } else {
                int entityId = (bean.getAppId() > 0) ? bean.getAppId() : serviceId;
                clusterId = componentInstanceDao.getClusterId(bean.getMstComponentId(), bean.getMstCommonVersionId(), bean.getMstComponentTypeId(), entityId, controllerId, bean.getAccountId());
            }
            if (clusterId > 0) break;
        }

        if (clusterId <= 0) {
            String originalIdentifier = bean.getIdentifier();
            String originalName = bean.getName();
            bean.setHostAddress(null);
            bean.setIdentifier(originalIdentifier + "_cluster");
            bean.setName(originalName + "_cluster");
            clusterId = addCluster(bean);
            bean.setIdentifier(originalIdentifier);
            bean.setName(originalName);
        }
        return clusterId;
    }

    private void addClusterMapping(ComponentInstanceBean bean, int clusterId) throws HealControlCenterException {
        CompClusterMappingBean compClusterMappingBean = new CompClusterMappingBean();
        compClusterMappingBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compClusterMappingBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compClusterMappingBean.setUserDetailsId(bean.getUserDetailsId());
        compClusterMappingBean.setAccountId(bean.getAccountId());
        compClusterMappingBean.setCompInstanceId(bean.getId());
        compClusterMappingBean.setClusterId(clusterId);
        int mappingId = componentInstanceDao.addCompClusterMapping(compClusterMappingBean);
        if (mappingId == -1) {
            String err = "Failed to add component instance cluster mapping for instance name-" + bean.getName();
            log.error(err);
            throw new HealControlCenterException(err);
        } else {
            log.info("Added Comp instance Cluster mapping  for comp instance name :{}, cluster_id:{}", bean.getName(), clusterId);
        }
    }

    private int addCluster(ComponentInstanceBean bean) throws HealControlCenterException {
        String err;
        int clusterId;

        setHostInfo(bean, true);
        bean.setIsCluster(1);
        //Differentiate cluster identifier from instance identifier. Same identifier can't be inserted in comp instance

        if (bean.getIsUpdate() == 0)
            clusterId = componentInstanceDao.addComponentInstance(bean);
        else
            clusterId = componentInstanceDao.updateComponentInstance(bean);

        if (clusterId != -1) {
            log.info("Comp instance cluster created/updated for comp instance name :{}, cluster_id:{}", bean.getName(), clusterId);
            addServiceDetails(clusterId, bean);
            if (bean.getIsUpdate() == 0) {
                //Add KPIs
                addKPIs(bean, clusterId);
                //Add Group KPIs
                addGroupKPIs(bean, clusterId, true);
            }
        } else {
            err = "Failed to add the comp instance data for comp instance name:" + bean.getName();
            log.error(err);
            throw new HealControlCenterException(err);
        }

        return clusterId;
    }

    private void addGroupKPIs(ComponentInstanceBean bean, int instanceId, boolean isCluster) throws HealControlCenterException {
            CommVersionKPIs commVersionKPIs = new CommVersionKPIs();
            commVersionKPIs.setAccountId(bean.getAccountId());
            commVersionKPIs.setMstCommonVersionId(bean.getMstCommonVersionId());
            List<ViewCommonVersionKPIsBean> viewCommonVersionKPIsBeansGroup = masterDataDao.getViewCommonVersionGroupKPIsData(commVersionKPIs.getMstCommonVersionId(), commVersionKPIs.getAccountId());
            if (viewCommonVersionKPIsBeansGroup == null) {
                String logs = "No group kpi is available for given master component version -" + bean.getMstCommonVersionId() +
                        ", mstComponentType-" + bean.getMstComponentTypeId();
                log.warn(logs);
                return;
            }
            if (isCluster) {
                final Set<Integer> clusterKpiIds = masterDataDao.getAllKpis().stream().filter(
                                kpi -> !kpi.getClusterOperation().equalsIgnoreCase(Constants.NONE))
                        .map(AllKpiList::getKpiId).collect(Collectors.toSet());

                viewCommonVersionKPIsBeansGroup = viewCommonVersionKPIsBeansGroup.stream().filter(
                        group -> clusterKpiIds.contains(group.getKpiId())).collect(Collectors.toList());
            }
            for (ViewCommonVersionKPIsBean viewCommonVersionKPIsBean : viewCommonVersionKPIsBeansGroup) {
                if (viewCommonVersionKPIsBean.getStatus() == 0) {
                    continue;
                }
                addEachGroupKPI(viewCommonVersionKPIsBean, bean, instanceId);
            }
    }

    private void addEachGroupKPI(ViewCommonVersionKPIsBean viewCommonVersionKPIsBean, ComponentInstanceBean bean, int instanceId) throws HealControlCenterException {

            int collectionInterval = viewCommonVersionKPIsBean.getDefaultCollectionInterval();
            int groupId = viewCommonVersionKPIsBean.getKpiGroupId();
            MasterKpiGroupBean groupBean = masterDataDao.getMasterKPIGroupList(bean.getAccountId(), groupId).get(0);
            if (groupBean == null) {
                String logs = "No group kpis found in master kpi group details table for group id-" + groupId;
                log.warn(logs);
                throw new HealControlCenterException(logs);
            }

            //only for discovered Group KPIs, add instance
            //Non-discovered ones, user has to manually add attributes entries in CC UI
            if (groupBean.getDiscovery() == 0) {
                return;
            }

            ProducerKpis producerKpis = new ProducerKpis();
            producerKpis.setMstKpiDetailsId(viewCommonVersionKPIsBean.getKpiId());
            producerKpis.setMstCompVersionId(bean.getMstComponentVersionId());
            producerKpis.setMstCompId(bean.getMstComponentId());
            producerKpis.setMstCompTypeId(bean.getMstComponentTypeId());
            producerKpis.setAccountId(bean.getAccountId());
            ViewProducerKPIsBean viewProducerKPIsBean = masterDataDao.getViewProducerGroupKPIsData(producerKpis.getMstKpiDetailsId(), producerKpis.getMstCompVersionId(),
                    producerKpis.getMstCompId(), producerKpis.getMstCompTypeId(), producerKpis.getAccountId());
            if (viewProducerKPIsBean == null) {
                String error = "No producers found for group kpi - " + viewCommonVersionKPIsBean.getKpiName();
                log.warn(error);
                return;
            }

            MstKpi mstKpi = new MstKpi();
            mstKpi.setAccountId(bean.getAccountId());
            mstKpi.setKpiId(viewProducerKPIsBean.getMstKpiDetailsId());
            MasterKPIDetailsBean kpiBean = masterDataDao.getMasterKPIDetailsData(mstKpi.getKpiId(), mstKpi.getAccountId());
            if (kpiBean == null) {
                String error = "No group kpis found in master kpi details table for producer -" +
                        viewProducerKPIsBean.getProducerName();
                log.warn(error);
                throw new HealControlCenterException(error);
            }

            CompInstanceKpiGroupDetailsBean compInstanceKpiGroupDetailsBean = new CompInstanceKpiGroupDetailsBean();
            compInstanceKpiGroupDetailsBean.setStatus(groupBean.getStatus());
            compInstanceKpiGroupDetailsBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            compInstanceKpiGroupDetailsBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            compInstanceKpiGroupDetailsBean.setUserDetailsId(bean.getUserDetailsId());
            compInstanceKpiGroupDetailsBean.setCompInstanceId(instanceId);
            compInstanceKpiGroupDetailsBean.setMstProducerKpiMappingId(viewProducerKPIsBean.getMstProducerKpiMappingId());
            compInstanceKpiGroupDetailsBean.setCollectionInterval(collectionInterval);
            compInstanceKpiGroupDetailsBean.setMstKpiDetailsId(viewCommonVersionKPIsBean.getKpiId());
            compInstanceKpiGroupDetailsBean.setIsDiscovery(groupBean.getDiscovery());
            compInstanceKpiGroupDetailsBean.setKpiGroupName(groupBean.getIdentifier());
            compInstanceKpiGroupDetailsBean.setMstKpiGroupId(groupId);
            compInstanceKpiGroupDetailsBean.setMstProducerId(viewProducerKPIsBean.getProducerId());
            compInstanceKpiGroupDetailsBean.setAttributeValue(Constants.ALL);
            compInstanceKpiGroupDetailsBean.setAliasName(Constants.ALL);

            int id = componentInstanceDao.addGroupCompInstanceKpiDetails(compInstanceKpiGroupDetailsBean);
            if (id == -1) {
                String error = "Unable to add group kpi for component instance id-" +
                        compInstanceKpiGroupDetailsBean.getCompInstanceId() + ", group kpi name-" +
                        kpiBean.getName();
                log.warn(error);
                throw new HealControlCenterException(error);
            }
            log.info("Added group kpi for component instance id-" +
                    compInstanceKpiGroupDetailsBean.getCompInstanceId() + ", group kpi name-" +
                    kpiBean.getName());

    }

    private void addKPIs(ComponentInstanceBean bean, int instanceId) throws HealControlCenterException {
            List<CompInstanceKpiDetailsBean> kpiList = componentInstanceDao.getDefaultCompInstanceKPIsData(bean.getMstComponentId(), bean.getMstCommonVersionId(), bean.getMstComponentVersionId(), bean.getMstComponentTypeId());
            if (kpiList != null && !kpiList.isEmpty()) {
                for (CompInstanceKpiDetailsBean kpiBean : kpiList) {
                    kpiBean.setCompInstanceId(instanceId);
                    kpiBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    kpiBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    kpiBean.setUserDetailsId(bean.getUserDetailsId());
                    int id = componentInstanceDao.addNonGroupCompInstanceKpiDetails(kpiBean);
                    if (id == -1) {
                        String err = "Unable to add KPIs -" + kpiBean.getMstKpiDetailsId() + " for component instance id-" + instanceId;
                        log.error(err);
                        throw new HealControlCenterException(err);
                    }
                    log.info("Added KPIs -" + kpiBean.getMstKpiDetailsId() + " for component instance id-" + instanceId);
                }
            } else {
                log.info("No KPIs found for component instance id-" + instanceId);
            }
    }

    private void addServiceDetails(int clusterId, ComponentInstanceBean bean) throws HealControlCenterException {
        final int controllerId = masterDataDao.getTagDetails(Constants.CONTROLLER_TAG, 1).getId();
            String err;
            if (bean.getIsUpdate() == 1) {
                int deleted = tagsDao.deleteTagMapping(controllerId, clusterId, Constants.COMP_INSTANCE_TABLE, bean.getAccountId());
                if (deleted == -1) {
                    err = "Failed to remove tag mapping info for comp instance name:" + bean.getName();
                    log.error(err);
                    throw new HealControlCenterException(err);
                }
            }
    }

    private void setHostInfo(ComponentInstanceBean bean, boolean isCluster) throws HealControlCenterException {
        List<MasterComponentTypeBean> masterComponentTypeBeanList = masterDataDao.getMasterComponentTypesData(Constants.DEFAULT_ACCOUNT_ID);
        MasterComponentTypeBean componentTypeBean = masterComponentTypeBeanList.stream().filter(masterComponentTypeBean -> masterComponentTypeBean.getName()
                .equalsIgnoreCase(Constants.COMPONENT_TYPE_HOST)).findAny().orElse(null);
        //Host ID info
        int hostId = bean.getHostId();
        if (isCluster) {
            if (bean.getIsHost() == 1) {
                hostId = 0;
            } else {
                try {
                    //Assuming that all the services are part of the same host.
                    List<Integer> hostIds = bindInDao
                            .getHostClusterId(Arrays.stream(bean.getServiceIds()).boxed().collect(Collectors.toList()), componentTypeBean != null ? componentTypeBean.getId() : 1);
                    if (hostIds.size() != 1) {
                        log.warn("Multiple/No host cluster Ids available for the provided services. Resetting hostId due to this conflict.");
                        hostId = 0;
                    } else {
                        hostId = hostIds.get(0);
                    }
                } catch (HealControlCenterException e) {
                    log.error("Unable to fetch the host clusterId for component instance cluster");
                    throw new HealControlCenterException(e.getMessage());
                }
            }
        }

        String parentIdentifier = bean.getParentIdentifier();
        String parentName = bean.getParentName();
        if (parentIdentifier != null || parentName != null) {
            //if clustered, then host id should be cluster id of host instance
            if (isCluster) {
                parentIdentifier = parentIdentifier + "_cluster";
                parentName = parentName + "_cluster";

            }
            ComponentInstanceBean instanceBean = componentInstanceDao.getActiveComponentInstanceByIdentifierAndName(parentIdentifier, parentName, bean.getAccountId());
            if (instanceBean == null) {
                String err = null;
                if (isCluster) {
                    CompClusterMappingBean clusterMappingBean = componentInstanceDao.getCompClusterDetails(bean.getParentIdentifier(), bean.getParentName(), bean.getAccountId());
                    if (clusterMappingBean != null) {
                        hostId = clusterMappingBean.getClusterId();
                    } else {
                        err = "Unable to find Cluster id for Parent instance identifier '" + bean.getParentIdentifier() + "' or name '" + bean.getParentName() + "' " + Constants.DOES_NOT_EXIST;
                    }
                } else {
                    err = "Parent instance identifier '" + parentIdentifier + "' or name '" + parentName + "' " + Constants.DOES_NOT_EXIST;
                }

                if (err != null) {
                    log.error(err);
                    throw new HealControlCenterException(err);
                }
            } else {
                hostId = instanceBean.getId();
            }
        }

        bean.setHostId(hostId);
        bean.setParentId(hostId);
    }

    private void moreServerValidations(ComponentInstanceBean bean) throws HealControlCenterException {
        final int controllerId = masterDataDao.getTagDetails(Constants.CONTROLLER_TAG, 1).getId();

        String err;
        int isUpdate = 0;
        //for node instances, create app level cluster
        int appId = 0; //it will be set only for node instances
        int serviceId = bean.getServiceIds()[0];
        int serviceTypeTagId = masterDataDao.getTagDetails(Constants.SERVICE_TYPE_TAG,1).getId();
        int tagMappingId = tagsDao.getTagMappingId(serviceTypeTagId, serviceId, Constants.CONTROLLER, Constants.SERVICE_TYPE_DEFAULT, Constants.KUBERNETES, bean.getAccountId());
        if (tagMappingId > 0)
            bean.setIsKubernetes(1);

        if (bean.getIsKubernetes() == 1 && bean.getIsHost() == 1) {
            appId = tagsDao.getTagMappingObjectId(controllerId, Constants.CONTROLLER, String.valueOf(serviceId), bean.getServiceIdentifiers().get(0), bean.getAccountId());
        }

        ComponentInstanceBean componentInstanceBean = componentInstanceDao.getComponentInstanceByIdentifier(bean.getIdentifier(), bean.getAccountId());
        if (componentInstanceBean != null) {
            TagMappingBean tagMappingBean = componentInstanceDao.getComponentInstanceTagMappingDetails(componentInstanceBean.getId(), controllerId, bean.getAccountId());
            if (tagMappingBean != null && componentInstanceBean.getStatus() == 0) {
                if (appId > 0) {
                    int tmpAppId = tagsDao.getTagMappingObjectId(controllerId, Constants.CONTROLLER, String.valueOf(tagMappingBean.getTagKey()), tagMappingBean.getTagValue(), bean.getAccountId());
                    if (appId == tmpAppId) {
                        log.info("Component Instance with identifier '" + bean.getIdentifier() + "' already exists. So Reactivating..");
                        isUpdate = 1;
                    }
                } else {
                    if (bean.getServiceIdentifiers().get(0).trim().equals(tagMappingBean.getTagValue())) {
                        log.info("Component Instance with identifier '" + bean.getIdentifier() + "' already exists. So Reactivating..");
                        isUpdate = 1;
                    }
                }
            } else {
                err = "Component Instance identifier '" + bean.getIdentifier() + "' already exists.";
                log.error(err);
                throw new HealControlCenterException(err);
            }
        } else {
            componentInstanceBean = componentInstanceDao.getActiveComponentInstance(null, bean.getName(), bean.getAccountId());
            if (componentInstanceBean != null) {
                TagMappingBean tagMappingBean = componentInstanceDao.getComponentInstanceTagMappingDetails(componentInstanceBean.getId(), controllerId, bean.getAccountId());
                if (tagMappingBean != null && bean.getServiceIdentifiers().get(0).trim().equals(tagMappingBean.getTagValue())) {
                    err = "Component Instance with name '" + bean.getName() + "' already exists.";
                    log.error(err);
                    throw new HealControlCenterException(err);
                }
            }
        }
        bean.setAppId(appId);
        bean.setIsUpdate(isUpdate);
    }

    /**
     * Adds component instance attributes to database.
     * @param bean Component instance bean
     * @throws Exception if insertion fails
     */
    private void addAttributes(ComponentInstanceBean bean, boolean isUpdate) throws Exception {
        List<CompInstanceAttributesBean> attributesList = bean.getAttributes();

        if (attributesList != null) {
            for (CompInstanceAttributesBean attribute : attributesList) {
                attribute.setCompInstanceId(bean.getId());
                attribute.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                int id;
                if (!isUpdate) {
                    attribute.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    id = componentInstanceDao.addComponentInstanceAttributes(attribute);
                } else {
                    id = componentInstanceDao.updateComponentInstanceAttributes(attribute);
                }

                if (id == -1) {
                    String err = "Unable to add/update attribute-" + attribute.getAttributeName() + " for component instance id-" + bean.getId();
                    log.error(err);
                    throw new HealControlCenterException(err);
                }
                log.info("Attribute-{} is added/updated for component instance id-{}", attribute.getAttributeName(), bean.getId());
            }
        } else {
            log.info("No Attributes found for component instance id-" + bean.getId());
        }
    }

    /**
     * Adds agent mapping for component instance.
     * @param instanceId Component instance ID
     * @param agentIdsMap Agent IDs map
     * @throws Exception if insertion fails
     */
    private void addAgentMapping(int instanceId, Map<Integer, String> agentIdsMap) throws Exception {
        if (!agentIdsMap.isEmpty()) {
            List<AgentCompInstMappingBean> beanList = new ArrayList<>();
            for (int agentId : agentIdsMap.keySet()) {
                beanList.add(AgentCompInstMappingBean.builder()
                        .compInstanceId(instanceId)
                        .agentId(agentId)
                        .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                        .build());
            }

            int[] mappingId = componentInstanceDao.addAgentCompInstMapping(beanList);
            if (mappingId == null) {
                String err = "Failed to add component instance agent mapping for instance id-" + instanceId;
                log.error(err);
                throw new HealControlCenterException(err);
            } else {
                log.info("Added Comp instance agent mapping  for comp instance id :{}", instanceId);
            }
        }
    }

    /**
     * Adds instances to Redis cache - exact equivalent to ComponentInstanceUtil.addInstancesToRedis from appsone-controlcenter.
     * This method follows the exact pattern and Redis key structure from the original implementation.
     * @param beanList List of component instance beans
     * @param accountIdentifier Account identifier
     * @return List of IdPojo objects
     * @throws Exception if Redis update fails
     */
    private List<IdPojo> addInstancesToRedis(List<ComponentInstanceBean> beanList, String accountIdentifier) throws Exception {
        List<IdPojo> idPojosList = new ArrayList<>();

        List<CompInstClusterDetails> instanceDetails = instanceRepo.getInstances(accountIdentifier);
        Account account = accountRepo.getAccount(accountIdentifier);
        int accId = account.getId();

        if (instanceDetails.isEmpty()) {
            instanceDetails = new ArrayList<>();
        }

        for (ComponentInstanceBean componentInstanceBean : beanList) {
            List<HostInstanceDetails> hostInstanceBean = getHostInstanceId(
                    componentInstanceBean.getHostAddress(), accId, componentInstanceBean.getIsDR());
            ClusterInstancePojo clusterInstanceMapping = getClusterInstanceMapping(componentInstanceBean.getId());
            List<String> clusterIdentifiers = Collections.singletonList(clusterInstanceMapping.getClusterIdentifier());

            CompInstClusterDetails clusterDetails = instanceDetails.parallelStream()
                    .filter(f -> f.getIdentifier().equalsIgnoreCase(clusterInstanceMapping.getClusterIdentifier()))
                    .findAny().orElse(null);

            if(clusterDetails == null) {
                log.info("Cluster details of this instance is not present in redis. Therefore, cluster details should be populated in redis");
                CompInstClusterDetails newClusterDetail = CompInstClusterDetails.builder()
                        .id(clusterInstanceMapping.getClusterId())
                        .name(clusterInstanceMapping.getClusterName())
                        .identifier(clusterInstanceMapping.getClusterIdentifier())
                        .status(clusterInstanceMapping.getStatus())
                        .createdTime(clusterInstanceMapping.getCreatedTime())
                        .updatedTime(clusterInstanceMapping.getUpdatedTime())
                        .lastModifiedBy(clusterInstanceMapping.getLastModifiedBy())
                        .componentId(clusterInstanceMapping.getComponentId())
                        .componentName(componentInstanceBean.getMstComponentName())
                        .componentTypeId(clusterInstanceMapping.getComponentTypeId())
                        .componentTypeName(componentInstanceBean.getMstComponentType())
                        .componentVersionId(clusterInstanceMapping.getComponentVersionId())
                        .componentVersionName(componentInstanceBean.getMstComponentVersion())
                        .commonVersionId(clusterInstanceMapping.getCommonVersionId())
                        .commonVersionName(componentInstanceBean.getMstCommonVersionName())
                        .supervisorId(clusterInstanceMapping.getSupervisorId())
                        .hostId(clusterInstanceMapping.getHostId())
                        .clusterIdentifiers(Collections.emptyList())
                        .hostName(hostInstanceBean.get(0).getHostInstanceName())
                        .hostAddress(clusterInstanceMapping.getHostAddress())
                        .isDR(clusterInstanceMapping.getIsDR())
                        .discovery(clusterInstanceMapping.getDiscovery())
                        .agentIds(componentInstanceBean.getAgentIdentifiers() != null ? componentInstanceBean.getAgentIdentifiers() : new ArrayList<>())
                        .parentInstanceId(clusterInstanceMapping.getParentInstanceId())
                        .accountId(clusterInstanceMapping.getAccountId())
                        .isCluster(clusterInstanceMapping.getIsCluster() == 1)
                        .build();
                instanceDetails.add(newClusterDetail);
                instanceRepo.updateInstances(accountIdentifier, instanceDetails);
                instanceRepo.updateInstanceByIdentifier(accountIdentifier, newClusterDetail);
                addCompInstKpiDetailsInRedis(accountIdentifier, newClusterDetail.getId(), newClusterDetail.getAccountId(), newClusterDetail.getIdentifier(), newClusterDetail.getComponentName());
            }

            CompInstClusterDetails newCompInstanceDetail = addCompInstanceDetailsInRedis(accountIdentifier, instanceDetails, componentInstanceBean, hostInstanceBean, clusterIdentifiers);

            addInstanceAttributesInRedis(accountIdentifier, componentInstanceBean);

            addCompInstKpiDetailsInRedis(accountIdentifier, componentInstanceBean.getId(), componentInstanceBean.getAccountId(), componentInstanceBean.getIdentifier(), componentInstanceBean.getMstComponentName());

            if (!componentInstanceBean.getAgentIdsMap().isEmpty()) {
                addAgentToInstanceMapping(accountIdentifier, componentInstanceBean);
            }

            com.heal.configuration.entities.BasicInstanceBean basicInstanceBean = getBasicInstanceBean(componentInstanceBean, clusterInstanceMapping);
            Set<BasicEntity> serviceDetailsSet = new HashSet<>();
            for (String serviceIdentifier : componentInstanceBean.getServiceIdentifiers()) {
                BasicEntity basicEntity = addCompInstanceDetailsAtServiceLevel(accountIdentifier, basicInstanceBean, serviceIdentifier);
                serviceDetailsSet.add(basicEntity);
            }
            List<BasicEntity> serviceDetails = serviceDetailsSet.parallelStream().collect(Collectors.toList());
            instanceRepo.updateInstanceWiseServices(accountIdentifier, newCompInstanceDetail.getIdentifier(), serviceDetails);
            idPojosList.add(IdPojo.builder()
                    .id(componentInstanceBean.getId())
                    .name(componentInstanceBean.getName())
                    .identifier(componentInstanceBean.getIdentifier())
                    .build());
        }
        return idPojosList;
    }

    /**
     * Gets host instance details - exact equivalent to BindInDataService.getHostInstanceId from appsone-controlcenter.
     * Uses exact JDBI to JDBC conversion with same query and parameters as original appsone-controlcenter.
     * Original JDBI method: BindInDataService.getHostInstanceId(hostCompTypeId, hostAddress, accId, isDR)
     * @param hostAddress Host address
     * @param accountId Account ID
     * @param isDR Environment/DR flag
     * @return List of HostInstanceDetails (exact match to appsone-controlcenter)
     */
    private List<HostInstanceDetails> getHostInstanceId(String hostAddress, int accountId, int isDR) {
        try {
            // Get host component type ID (same as done in validation)
            MasterComponentTypeBean hostComponentType = masterComponentDao.getMasterComponentTypeUsingName("Host", String.valueOf(accountId));
            if (hostComponentType == null) {
                log.error("Host component type not found for account: {}", accountId);
                return new ArrayList<>();
            }

            int hostCompTypeId = hostComponentType.getId();

            // Use exact JDBI to JDBC converted query from appsone-controlcenter
            return componentInstanceDao.getHostInstanceId(hostCompTypeId, hostAddress, accountId, isDR);
        } catch (Exception e) {
            log.error("Error getting host instance details for hostAddress: {}, accountId: {}, isDR: {}", hostAddress, accountId, isDR, e);
            return new ArrayList<>();
        }
    }

    /**
     * Gets cluster instance mapping - converted from appsone-controlcenter JDBI to JDBC.
     * Original appsone-controlcenter method signature:
     * public ClusterInstancePojo getClusterInstanceMapping(int instanceId, Handle handle) throws ControlCenterException
     *
     * @param instanceId Component instance ID
     * @return ClusterInstancePojo (exact match to appsone-controlcenter)
     * @throws HealControlCenterException if error occurs during database operation
     */
    public ClusterInstancePojo getClusterInstanceMapping(int instanceId) throws HealControlCenterException {
        try {
            log.debug("Fetching cluster mapping details for instanceId [{}]", instanceId);

            // JDBC equivalent of original JDBI dao.getClusterInstanceMapping(instanceId)
            ClusterInstancePojo clusterMapping = componentInstanceDao.getClusterInstanceMapping(instanceId);

            if (clusterMapping != null) {
                log.debug("Successfully fetched cluster mapping for instanceId [{}]", instanceId);
                return clusterMapping;
            }
        } catch (Exception e) {
            log.error("Error while fetching cluster mapping details for instanceId [{}]", instanceId, e);
            throw new HealControlCenterException("Error while fetching cluster mapping details for instanceId [" + instanceId + "]");
        }
        return null;
    }

    /**
     * Adds component instance details in Redis - equivalent to addCompInstanceDetailsInRedis from appsone-controlcenter.
     * @param accountIdentifier Account identifier
     * @param instanceDetails List of instance details
     * @param componentInstanceBean Component instance bean
     * @param hostInstanceBean Host instance details (using HostInstanceDetails POJO)
     * @param clusterIdentifiers Cluster identifiers
     * @return CompInstClusterDetails object
     */
    private CompInstClusterDetails addCompInstanceDetailsInRedis(String accountIdentifier, List<CompInstClusterDetails> instanceDetails,
                                                               ComponentInstanceBean componentInstanceBean, List<HostInstanceDetails> hostInstanceBean,
                                                               List<String> clusterIdentifiers) {
        try {
            CompInstClusterDetails newCompInstanceDetail = CompInstClusterDetails.builder()
                    .id(componentInstanceBean.getId())
                    .name(componentInstanceBean.getName())
                    .identifier(componentInstanceBean.getIdentifier())
                    .status(componentInstanceBean.getStatus())
                    .createdTime(componentInstanceBean.getCreatedTime())
                    .updatedTime(componentInstanceBean.getUpdatedTime())
                    .lastModifiedBy(componentInstanceBean.getUserDetailsId())
                    .componentId(componentInstanceBean.getMstComponentId())
                    .componentName(componentInstanceBean.getMstComponentName())
                    .componentTypeId(componentInstanceBean.getMstComponentTypeId())
                    .componentTypeName(componentInstanceBean.getMstComponentType())
                    .componentVersionId(componentInstanceBean.getMstComponentVersionId())
                    .componentVersionName(componentInstanceBean.getMstComponentVersion())
                    .commonVersionId(componentInstanceBean.getMstCommonVersionId())
                    .commonVersionName(componentInstanceBean.getMstCommonVersionName())
                    .supervisorId(componentInstanceBean.getSupervisorId())
                    .hostId(!hostInstanceBean.isEmpty() ? hostInstanceBean.get(0).getHostInstanceId() : 0)
                    .clusterIdentifiers(clusterIdentifiers)
                    .hostName(!hostInstanceBean.isEmpty() ? hostInstanceBean.get(0).getHostInstanceName() : "")
                    .hostAddress(componentInstanceBean.getHostAddress())
                    .isDR(componentInstanceBean.getIsDR())
                    .discovery(componentInstanceBean.getDiscovery())
                    .agentIds(componentInstanceBean.getAgentIdentifiers() != null ? componentInstanceBean.getAgentIdentifiers() : new ArrayList<>())
                    .parentInstanceId(componentInstanceBean.getParentId())
                    .accountId(componentInstanceBean.getAccountId())
                    .isCluster(componentInstanceBean.getIsCluster() == 1)
                    .build();

            instanceDetails.add(newCompInstanceDetail);
            instanceRepo.updateInstances(accountIdentifier, instanceDetails);
            instanceRepo.updateInstanceByIdentifier(accountIdentifier, newCompInstanceDetail);

            return newCompInstanceDetail;
        } catch (Exception e) {
            log.error("Error adding component instance details in Redis for instance: {}", componentInstanceBean.getIdentifier(), e);
            throw new RuntimeException("Failed to add component instance details in Redis", e);
        }
    }

    /**
     * Adds instance attributes in Redis - equivalent to addInstanceAttributesInRedis from appsone-controlcenter.
     * Uses DAO directly instead of service for JDBC operations.
     * @param accountIdentifier Account identifier
     * @param componentInstanceBean Component instance bean
     */
    private void addInstanceAttributesInRedis(String accountIdentifier, ComponentInstanceBean componentInstanceBean) {
            List<com.heal.configuration.pojos.InstanceAttributes> instanceAttributes = componentInstanceDao.getInstanceAttributeDetailsForCompInstanceId(componentInstanceBean.getAccountId(), componentInstanceBean.getId());

            if (instanceAttributes.isEmpty()) {
                log.error("Could not find instance attributes details for component instance [{}]", componentInstanceBean.getId());
                return;
            }
            instanceRepo.updateAttributeDetails(accountIdentifier, componentInstanceBean.getIdentifier(), instanceAttributes);
    }

    /**
     * Adds component instance KPI details to Redis - converted from appsone-controlcenter JDBI to JDBC.
     * Original appsone-controlcenter method used JDBI with Handle parameters.
     * This implementation uses Spring JDBC with JdbcTemplate for database operations.
     *
     * @param accountIdentifier Account identifier
     * @param compInstanceBeanId Component instance ID
     * @param accountId Account ID
     * @param compInstanceBeanIdentifier Component instance identifier
     * @param compInstanceMstComponentName Component name
     * @throws HealControlCenterException if error occurs during database operation
     */
    private void addCompInstKpiDetailsInRedis(String accountIdentifier, int compInstanceBeanId, int accountId, String compInstanceBeanIdentifier, String compInstanceMstComponentName) throws HealControlCenterException {
        List<CompInstKpiEntity> compInstKpiEntityList = new ArrayList<>();

        List<ComponentKpiEntity> componentKpiDetails = componentRepo.getComponentKpiDetails(accountIdentifier, compInstanceMstComponentName);
        List<GroupKpiAttributeMapping> groupKpiAttributeMapping = bindInDao.getGroupKpiAttributeMapping(accountId, compInstanceBeanId);
        List<CompInstKpiMapping> compInstKpiMappingList = componentInstanceDao.getCompInstKpiMapping(compInstanceBeanId);


        for (CompInstKpiMapping compInstKpiMapping : compInstKpiMappingList) {
            ComponentKpiEntity componentKpiEntity = componentKpiDetails.parallelStream().filter(f -> f.getId() == compInstKpiMapping.getMstKpiId()).findAny().orElse(null);
            if(componentKpiEntity == null){
                continue;
            }
            Map<String, String> attributeValues = groupKpiAttributeMapping.parallelStream()
                    .filter(f -> f.getKpiId() == componentKpiEntity.getId())
                    .collect(Collectors.toMap(GroupKpiAttributeMapping::getAttributeValue, GroupKpiAttributeMapping::getAliasName));

            CompInstKpiEntity compInstKpiEntityObject = CompInstKpiEntity.builder()
                    .compInstKpiId(compInstKpiMapping.getCompInstKpiId())
                    .collectionInterval(compInstKpiMapping.getCollectionInterval())
                    .status(compInstKpiMapping.getStatus())
                    .defaultProducerId(compInstKpiMapping.getMstProducerId())
                    .producerKpiMappingId(compInstKpiMapping.getMstProducerKpiMappingId())
                    .isBaseMetric(componentKpiEntity.getIsBaseMetric())
                    .notification(compInstKpiMapping.getNotification())
                    .attributeValues(attributeValues)
                    .custom(componentKpiEntity.getCustom())
                    .discovery(componentKpiEntity.getDiscovery())
                    .name(componentKpiEntity.getName())
                    .id(componentKpiEntity.getId())
                    .type(componentKpiEntity.getType())
                    .isGroup(componentKpiEntity.getIsGroup())
                    .groupName(componentKpiEntity.getGroupName())
                    .groupStatus(componentKpiEntity.getGroupStatus())
                    .status(componentKpiEntity.getStatus())
                    .groupId(componentKpiEntity.getGroupId())
                    .groupIdentifier(componentKpiEntity.getGroupIdentifier())
                    .unit(componentKpiEntity.getUnit())
                    .aggOperation(componentKpiEntity.getAggOperation())
                    .rollupOperation(componentKpiEntity.getRollupOperation())
                    .clusterAggType(componentKpiEntity.getClusterAggType())
                    .instanceAggType(componentKpiEntity.getInstanceAggType())
                    .identifier(componentKpiEntity.getIdentifier())
                    .availableForAnalytics(componentKpiEntity.getAvailableForAnalytics())
                    .categoryDetails(componentKpiEntity.getCategoryDetails())
                    .valueType(componentKpiEntity.getValueType())
                    .dataType(componentKpiEntity.getDataType())
                    .isInfo(componentKpiEntity.getIsInfo())
                    .resetDeltaValue(componentKpiEntity.getResetDeltaValue())
                    .deltaPerSec(componentKpiEntity.getDeltaPerSec())
                    .cronExpression(componentKpiEntity.getCronExpression())
                    .description(componentKpiEntity.getDescription())
                    .natureId(componentKpiEntity.getNatureId())
                    .isComputed(componentKpiEntity.getIsComputed())
                    .computedKpiPojo(componentKpiEntity.getComputedKpiPojo())
                    .build();

            compInstKpiEntityList.add(compInstKpiEntityObject);
            instanceRepo.updateKpiDetailsForKpiId(accountIdentifier, compInstanceBeanIdentifier, compInstKpiEntityObject);
            instanceRepo.updateKpiDetailsForKpiIdentifier(accountIdentifier, compInstanceBeanIdentifier, compInstKpiEntityObject);
        }
        instanceRepo.updateKpiDetails(accountIdentifier, compInstanceBeanIdentifier, compInstKpiEntityList);
    }

    /**
     * Adds agent to instance mapping - equivalent to addAgentToInstanceMapping from appsone-controlcenter.
     * @param accountIdentifier Account identifier
     * @param componentInstanceBean Component instance bean
     */
    private void addAgentToInstanceMapping(String accountIdentifier, ComponentInstanceBean componentInstanceBean) {

            Map<Integer, String> agentIdsMap = componentInstanceBean.getAgentIdsMap();
            for (int agentId : agentIdsMap.keySet()) {
                List<BasicEntity> agentToInstanceMapping = agentDao.getAgentToInstanceMapping(componentInstanceBean.getId(), componentInstanceBean.getAccountId(), agentId);
                List<BasicEntity> existingAgentInstanceMappingDetailsFromRedis = agentRepo.getAgentInstanceMappingDetails(accountIdentifier, componentInstanceBean.getAgentIdsMap().get(agentId));

                if(existingAgentInstanceMappingDetailsFromRedis.isEmpty()){
                    existingAgentInstanceMappingDetailsFromRedis = new ArrayList<>();
                }

                for (BasicEntity basicEntity : agentToInstanceMapping) {
                    existingAgentInstanceMappingDetailsFromRedis.add(BasicEntity.builder()
                            .id(basicEntity.getId())
                            .status(basicEntity.getStatus())
                            .createdTime(basicEntity.getCreatedTime())
                            .updatedTime(basicEntity.getUpdatedTime())
                            .name(basicEntity.getName())
                            .identifier(basicEntity.getIdentifier())
                            .lastModifiedBy(basicEntity.getLastModifiedBy())
                            .build());
                }
                agentRepo.updateAgentInstanceMappingDetails(accountIdentifier, componentInstanceBean.getAgentIdsMap().get(agentId),existingAgentInstanceMappingDetailsFromRedis);
            }
    }

    /**
     * Gets basic instance bean - equivalent to getBasicInstanceBean from appsone-controlcenter.
     * @param componentInstanceBean Component instance bean
     * @param clusterInstanceMapping Cluster instance mapping
     * @return BasicInstanceBean object
     */
    private com.heal.configuration.entities.BasicInstanceBean getBasicInstanceBean(ComponentInstanceBean componentInstanceBean, ClusterInstancePojo clusterInstanceMapping) {
            return com.heal.configuration.entities.BasicInstanceBean.builder()
                    .id(componentInstanceBean.getId())
                    .name(componentInstanceBean.getName())
                    .identifier(componentInstanceBean.getIdentifier())
                    .status(componentInstanceBean.getStatus())
                    .createdTime(componentInstanceBean.getCreatedTime())
                    .updatedTime(componentInstanceBean.getUpdatedTime())
                    .lastModifiedBy(componentInstanceBean.getUserDetailsId())
                    .componentId(componentInstanceBean.getMstComponentId())
                    .componentTypeId(componentInstanceBean.getMstComponentTypeId())
                    .componentVersionId(componentInstanceBean.getMstComponentVersionId())
                    .commonVersionId(componentInstanceBean.getMstCommonVersionId())
                    .clusterId(clusterInstanceMapping.getClusterId())
                    .clusterIdentifier(clusterInstanceMapping.getClusterIdentifier())
                    .accountId(componentInstanceBean.getAccountId())
                    .build();
    }

    /**
     * Adds component instance details at service level - equivalent to addCompInstanceDetailsAtServiceLevel from appsone-controlcenter.
     * @param accountIdentifier Account identifier
     * @param basicInstanceBean Basic instance bean
     * @param serviceIdentifier Service identifier
     * @return BasicEntity object
     */
    private BasicEntity addCompInstanceDetailsAtServiceLevel(String accountIdentifier, com.heal.configuration.entities.BasicInstanceBean basicInstanceBean, String serviceIdentifier) {
            Service serviceConfigurationDetail = serviceRepo.getServiceConfigurationByIdentifier(accountIdentifier, serviceIdentifier);

            if(serviceConfigurationDetail == null){
                log.error("Could not find service detail for given service identifier [{}] and account identifier [{}] ", serviceIdentifier, accountIdentifier);
                return null;
            }
            basicInstanceBean.setConcernedConfigId(serviceConfigurationDetail.getId());
            basicInstanceBean.setConcernedConfigIdentifier(serviceIdentifier);
            List<com.heal.configuration.entities.BasicInstanceBean> serviceInstances = serviceRepo.getServiceInstances(accountIdentifier, serviceIdentifier);

            if(serviceInstances.isEmpty()){
                serviceInstances = new ArrayList<>();
            }

            serviceInstances.add(basicInstanceBean);
            serviceRepo.updateServiceInstances(accountIdentifier, serviceIdentifier, serviceInstances);

            return BasicEntity.builder()
                    .id(serviceConfigurationDetail.getId())
                    .status(serviceConfigurationDetail.getStatus())
                    .createdTime(serviceConfigurationDetail.getCreatedTime())
                    .updatedTime(serviceConfigurationDetail.getUpdatedTime())
                    .name(serviceConfigurationDetail.getName())
                    .identifier(serviceConfigurationDetail.getIdentifier())
                    .lastModifiedBy(serviceConfigurationDetail.getLastModifiedBy())
                    .build();
    }
}
