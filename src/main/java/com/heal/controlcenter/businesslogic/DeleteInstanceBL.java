package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.*;
import com.heal.configuration.util.DateHelper;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.ComponentInstanceBean;
import com.heal.controlcenter.beans.TagDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.config.OpenSearchConfig;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.entity.ComponentInstanceDao;
import com.heal.controlcenter.dao.opensearch.OpenSearchRepo;
import com.heal.controlcenter.dao.redis.AgentRepo;
import com.heal.controlcenter.dao.redis.InstanceRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.DeleteInstancePojo;

import com.heal.controlcenter.util.*;
import org.opensearch.client.opensearch.core.DeleteByQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Business logic for deleting component instances.
 * Handles validation, database operations, and Redis cache updates.
 */
@Slf4j
@Service
public class DeleteInstanceBL implements BusinessLogic<DeleteInstancePojo, UtilityBean<DeleteInstancePojo>, String> {

    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final ComponentInstanceDao componentInstanceDao;
    private final InstanceRepo instanceRepo;
    private final MasterDataDao masterDataDao;
    private final ServiceRepo serviceRepo;
    private final OpenSearchRepo openSearchRepo;
    private final AgentRepo agentRepo

    public DeleteInstanceBL(ClientValidationUtils clientValidationUtils,
                                      ServerValidationUtils serverValidationUtils,
                                      ComponentInstanceDao componentInstanceDao,
                                      InstanceRepo instanceRepo,
                                      MasterDataDao masterDataDao,
                                      ServiceRepo serviceRepo,
                                      OpenSearchRepo openSearchRepo,
                                      AgentRepo agentRepo) {
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.componentInstanceDao = componentInstanceDao;
        this.instanceRepo = instanceRepo;
        this.masterDataDao = masterDataDao;
        this.serviceRepo = serviceRepo;
        this.openSearchRepo = openSearchRepo;
        this.agentRepo = agentRepo;
    }

    /**
     * Performs client-side validation for component instance deletion requests.
     * Ensures the account identifier is valid and the list of instance identifiers is not empty.
     *
     * @param arguments The DeleteInstancePojo, accountIdentifier, BasicUserDetails, and optional instanceRemovalStatus.
     * @return A UtilityBean containing the validated request body and metadata.
     * @throws ClientException If validation fails (e.g., empty instance identifiers list).
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<DeleteInstancePojo> clientValidation(Object... arguments) throws ClientException {
        try {
            DeleteInstancePojo requestBody = (DeleteInstancePojo) arguments[0];
            String accountIdentifier = (String) arguments[1];
            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            log.debug("[clientValidation] Start - accountIdentifier: {}, requestBody: {}",
                    accountIdentifier, requestBody);
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            // Validate request body
            if (requestBody == null) {
                throw new ClientException("Request body cannot be null");
            }

            if (requestBody.getInstances() == null || requestBody.getInstances().isEmpty()) {
                throw new ClientException("Instance identifiers list cannot be null or empty");
            }

            // Process and validate instances list
            List<String> instances = requestBody.getInstances();

            // Remove duplicates using parallel stream
            instances = instances.parallelStream().distinct().collect(Collectors.toList());

            // Validate each instance identifier
            for (String instance : instances) {
                if (instance == null || instance.trim().isEmpty()) {
                    log.error("Found empty/null Component Instance(s) name in request body.");
                    throw new ClientException("Found empty/null Component Instance(s) name in request body.");
                }
            }

            // Set the processed instances back to the request body
            requestBody.setInstances(instances);

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            log.debug("[clientValidation] Request params map created: {}", requestParamsMap);

            UtilityBean<DeleteInstancePojo> utilityBean = UtilityBean.<DeleteInstancePojo>builder()
                    .requestParams(requestParamsMap)
                    .pojoObject(requestBody)
                    .metadata(new HashMap<>())
                    .basicUserDetails(basicUserDetails)
                    .build();

            log.debug("[clientValidation] End - utilityBean: {}", utilityBean);
            return utilityBean;

        } catch (Exception e) {
            log.error("[clientValidation] Error during client validation", e);
            throw new ClientException("Client validation failed: " + e.getMessage());
        }
    }

    /**
     * Performs server-side validation for deleting component instances.
     * Checks user access, component instance existence, and validates deletion constraints.
     *
     * @param utilityBean UtilityBean containing the request and metadata.
     * @return UtilityBean with validated DeleteInstancePojo for deletion.
     * @throws ServerException if validation fails (e.g., instance not found, access denied).
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<DeleteInstancePojo> serverValidation(UtilityBean<DeleteInstancePojo> utilityBean) throws ServerException {
        log.debug("[serverValidation] Start - utilityBean: {}", utilityBean);
        String userId = utilityBean.getMetadata().get(Constants.USER_ID_KEY).toString();
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        log.debug("[serverValidation] userId: {}, accountIdentifier: {}", userId, accountIdentifier);
        
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();

        List<String> instanceIdentifiers = utilityBean.getPojoObject().getInstances();

        log.debug("[serverValidation] Instance identifiers: {}", instanceIdentifiers);

        try {
            List<ComponentInstanceBean> instanceBeanList = new ArrayList<>();

            for (String instanceIdentifier : instanceIdentifiers) {
                ComponentInstanceBean instanceBean = componentInstanceDao.getActiveComponentInstanceByIdentifierAndName(
                        instanceIdentifier, null, accountId);

                if (instanceBean == null) {
                    String err = "Component Instance Identifier:" + instanceIdentifier +
                               ", accountId:" + accountIdentifier + " does not exist";
                    log.error(err);
                    throw new ServerException(err);
                }
                instanceBeanList.add(instanceBean);
            }

            // Sort by isCluster (following original logic - clusters processed first)
            instanceBeanList = instanceBeanList.stream()
                    .sorted(Comparator.comparing(ComponentInstanceBean::getIsCluster))
                    .collect(Collectors.toList());

            // Set the validated instance beans in the POJO
            utilityBean.getPojoObject().setInstanceBeanList(instanceBeanList);

        } catch (HealControlCenterException e) {
            log.error("[serverValidation] Error during server validation", e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }

        utilityBean.getMetadata().put(Constants.ACCOUNT, account);

        UtilityBean<DeleteInstancePojo> validatedBean = UtilityBean.<DeleteInstancePojo>builder()
                .requestParams(utilityBean.getRequestParams())
                .pojoObject(utilityBean.getPojoObject())
                .metadata(utilityBean.getMetadata())
                .build();

        log.debug("[serverValidation] End - validatedBean: {}", validatedBean);
        return validatedBean;
    }

    /**
     * Processes the deletion of component instances and updates Redis cache.
     *
     * @param utilityBean UtilityBean containing DeleteInstancePojo to delete and metadata.
     * @return Success message if deletion is successful.
     * @throws DataProcessingException if any error occurs during deletion or Redis update.
     */
    @Override
    @LogExecutionAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public String process(UtilityBean<DeleteInstancePojo> utilityBean) throws DataProcessingException {
        log.debug("[process] Start - utilityBean: {}", utilityBean);
        DeleteInstancePojo deleteInstancePojo = utilityBean.getPojoObject();
        Account account = (Account) utilityBean.getMetadata().get(Constants.ACCOUNT);

        List<String> instanceIdentifiers = deleteInstancePojo.getInstances();
        log.debug("[process] Processing {} component instances for deletion", instanceIdentifiers.size());

        try {

            List<CompInstClusterDetails> compInstClusterDetails = instanceRepo.getInstances(account.getIdentifier());
            for (ComponentInstanceBean bean : utilityBean.getPojoObject().getInstanceBeanList()) {
                remComponentInstance(bean, utilityBean);
            }
            List<ComponentInstanceBean> clusterInstanceBeanList = addClusterCompInstanceBeanForInstances(utilityBean, compInstClusterDetails);
            for (ComponentInstanceBean clusterInstanceBean : clusterInstanceBeanList) {
                remComponentInstance(clusterInstanceBean, conn, utilityBean);
            }

            // Process each component instance for deletion (following original logic)
            for (ComponentInstanceBean bean : deleteInstancePojo.getInstanceBeanList()) {
                removeComponentInstance(bean, utilityBean);
            }

            // Handle cluster instances (following original logic)
            List<ComponentInstanceBean> clusterInstanceBeanList = addClusterCompInstanceBeanForInstances(utilityBean);
            for (ComponentInstanceBean clusterInstanceBean : clusterInstanceBeanList) {
                removeComponentInstance(clusterInstanceBean, utilityBean);
            }

            String message = "Component instances deleted successfully";
            log.debug("[process] End - message: {}", message);
            return message;

        } catch (Exception e) {
            log.error("[process] Error during component instance deletion", e);
            throw new DataProcessingException("Failed to delete component instances: " + e.getMessage());
        }
    }

    private void remComponentInstance(ComponentInstanceBean bean, UtilityBean<DeleteInstancePojo> utilityBean) throws HealControlCenterException {
        Account account = (Account) utilityBean.getMetadata().get(Constants.ACCOUNT);
        String accountIdentifier = account.getIdentifier();
        String instanceIdentifier = bean.getIdentifier();

        TagDetailsBean tagDetailsBean = masterDataDao.getTagDetails(Constants.CONTROLLER_TAG,1);
        int tagId = 1;
        if (tagDetailsBean == null) {
            log.warn("Exception while fetching tag id for 'Controller' type. Setting tag id value to default i.e 1.");
        } else {
            tagId = tagDetailsBean.getId();
        }

        long lastDataCollectionTime = serviceRepo.getInstanceHealthMapForAccount(accountIdentifier, instanceIdentifier);

        log.info("Last data collection of instanceName:{}, instanceIdentifier:{}, accountIdentifier:{}, time:{}, instance removal:{}", bean.getName(), instanceIdentifier, accountIdentifier, lastDataCollectionTime, utilityBean.getPojoObject().getInstanceRemovalStatus());
        if (utilityBean.getPojoObject().getInstanceRemovalStatus() != null && utilityBean.getPojoObject().getInstanceRemovalStatus()) {

            // In-case of cluster deletion request, check any instances are mapped for this cluster.
            if (bean.getIsCluster() == 1) {
                int countOfMappedInstances = componentInstanceDao.checkInstExistanceForCluster(bean.getId());
                if (countOfMappedInstances > 0) {
                    String err = "Some instances are still mapped to the Cluster with identifier " + instanceIdentifier + ". Please delete the mapped instances first.";
                    log.error(err);
                    throw new HealControlCenterException(err);
                }
            }

            hardDeleteInPercona(bean, tagId);

            //  delete the details of instance from openSearch
            if (lastDataCollectionTime <= 0) {
                hardDeleteInOpenSearch(bean, accountIdentifier);
            }

            deleteInstanceDetailsFromRedis(accountIdentifier, bean);
        } else if (utilityBean.getPojoObject().getInstanceRemovalStatus() == null) {
            if (ConfProperties.getBoolean(Constants.INSTANCE_REMOVAL_STATUS_PERCONA, Constants.INSTANCE_REMOVAL_STATUS_PERCONA_DEFAULT)) {
                hardDeleteInPercona(bean, tagId);
            } else {
                softDelete(bean, accountIdentifier);
            }

            //  delete the details of instance from openSearch
            if (ConfProperties.getBoolean(Constants.INSTANCE_REMOVAL_STATUS_OPENSEARCH, Constants.INSTANCE_REMOVAL_STATUS_OPENSEARCH_DEFAULT) && lastDataCollectionTime <= 0) {
                hardDeleteInOpenSearch(bean, accountIdentifier);
            }

            deleteInstanceDetailsFromRedis(accountIdentifier, bean);
        } else {
            softDeleteInDatabase(bean);
            deleteInstanceDetailsFromRedis(accountIdentifier, bean);
        }
    }

    private void softDelete(ComponentInstanceBean bean, String accountIdentifier) throws HealControlCenterException {
        log.info("Perform soft delete for instanceName:{}, instanceIdentifier:{}, accountIdentifier:{}", bean.getName(), bean.getIdentifier(), accountIdentifier);
         /*
        Set status = 0 in comp_instance table only
         */
        int res = compInstanceDataService.updateComponentInstanceStatusById(bean.getId());
        if (res == -1) {
            String err = "Exception while inactivating component instance with identifier '[" + bean.getIdentifier() + "]' in comp_instance table.";
            log.error(err);
            throw new HealControlCenterException(err);
        }
    }

    private void hardDeleteInOpenSearch(ComponentInstanceBean bean, String accountIdentifier) throws HealControlCenterException {
        long startDate = DateTimeUtil.getDateFromString(bean.getCreatedTime()).getTime();
        String instanceIdentifier = bean.getIdentifier();
        long endDate = System.currentTimeMillis();

        List<DateWeekBean> totalWeeks = DateHelper.getDatesWeeksAsString(startDate, endDate, "GMT");
        String[] indexes = (ConfProperties.getString(Constants.INSTANCE_REMOVAL_OPENSEARCH_INDEXES, Constants.INSTANCE_REMOVAL_OPENSEARCH_INDEXES_DEFAULT))
                .split(",");

        if (indexes.length == 0) {
            log.info("No indices present for deletion");
            throw new HealControlCenterException("No indices present for deletion");
        }

        log.info("Number of indexes found for instanceName:{}, instanceIdentifier:{}, indexes:{}, accountIdentifier:{}", bean.getName(), bean.getIdentifier(), indexes, accountIdentifier);
        for (String indexDetails : indexes) {

            String[] indexData = indexDetails.split(":", 2);
            totalWeeks.parallelStream().forEach(weekYear -> {
                String tempIndex = indexData[0] + "_" + accountIdentifier + "_" + weekYear.getIndexYearWeek();
                String termClause = (indexData.length == 1 ? "compInstanceIdentifier" : indexData[1]);
                try {
                    DeleteByQueryResponse deleteResponse = openSearchRepo.deleteDocByInstanceIdentifier(tempIndex, instanceIdentifier, termClause, accountIdentifier, indexData[0]);
                    if (deleteResponse == null) {
                        throw new HealControlCenterException("Unable to delete the KPI documents from Opensearch indexes");
                    } else if (deleteResponse.deleted() != null && deleteResponse.deleted() > 0) {
                        log.debug("Document with instanceIdentifier:{} for index:{}, termClause:{} deleted successfully", instanceIdentifier, tempIndex, termClause);
                    } else {
                        log.debug("Document with instanceIdentifier:{} for index:{}, termClause:{} does not exist", instanceIdentifier, tempIndex, termClause);
                    }
                } catch (IOException | HealControlCenterException e) {
                    log.error("Exception while deleting instance details in opensearch for index: {}, instance: {}", tempIndex, instanceIdentifier, e);
                    throw new RuntimeException(e);
                }
            });
        }
    }

    private void hardDeleteInPercona(ComponentInstanceBean bean, int tagId) throws HealControlCenterException {
        /*
        delete from all dependency tables
         */

        checkExecutionStatus(componentInstanceDao.deleteCompInstanceKpiGroupDetailsWithInstId(bean.getId()),
                bean.getIdentifier(), "comp_instance_kpi_group_details");
        checkExecutionStatus(componentInstanceDao.deleteCompInstanceKpiDetailsWithInstId(bean.getId()),
                bean.getIdentifier(), "comp_instance_kpi_details");

        checkExecutionStatus(componentInstanceDao.deleteJimAgentMappingWithInstId(bean.getId()),
                bean.getIdentifier(), "jim_agent");
        checkExecutionStatus(componentInstanceDao.deleteJimTransactionMappingWithInstId(bean.getId()),
                bean.getIdentifier(), "jim_transaction_mapping");
        checkExecutionStatus(componentInstanceDao.deleteAgentCompInstanceMappingWithInstId(bean.getId()),
                bean.getIdentifier(), "agent_comp_instance_mapping");
        checkExecutionStatus(componentInstanceDao.deleteCompInstanceMaintenanceMappingWithInstId(bean.getId()),
                bean.getIdentifier(), "comp_instance_maintenance_mapping");

        // new table delete
        checkExecutionStatus(componentInstanceDao.deleteCompInstanceForensicDetailsWithInstId(bean.getId()),
                bean.getIdentifier(), "comp_instance_forensic_details");
        checkExecutionStatus(componentInstanceDao.deleteCompInstanceHealthWithInstId(bean.getId()),
                bean.getIdentifier(), "comp_instance_health");
        checkExecutionStatus(componentInstanceDao.deleteCompInstanceKpisHealthWithInstId(bean.getId()),
                bean.getIdentifier(), "comp_instance_kpis_health");
        checkExecutionStatus(componentInstanceDao.deleteCompInstanceKpiConfigurationWithInstId(bean.getId()),
                bean.getIdentifier(), "comp_instance_kpi_configuration");

        if (bean.getIsCluster() == 0) {

            checkExecutionStatus(componentInstanceDao.deleteCompInstanceAttributeValuesWithInstId(bean.getId()),
                    bean.getIdentifier(), "comp_instance_attribute_values");
            checkExecutionStatus(componentInstanceDao.deleteCompInstanceKpiThresholdDetailsWithInstId(bean.getId()),
                    bean.getIdentifier(), "comp_instance_kpi_threshold_details");
            checkExecutionStatus(componentInstanceDao.deleteComponentClusterMappingWithInstId(bean.getId()),
                    bean.getIdentifier(), "component_cluster_mapping");
        } else {

            checkExecutionStatus(componentInstanceDao.deleteComponentClusterMappingWithClusterId(bean.getId()),
                    bean.getIdentifier(), "component_cluster_mapping");
            checkExecutionStatus(componentInstanceDao.deleteTagMappingWithClusterId(bean.getId(),
                    tagId, Constants.COMP_INSTANCE_TABLE), bean.getIdentifier(), "tag_mapping");
        }
        checkExecutionStatus(componentInstanceDao.deleteCompInstanceMetadataWithInstId(bean.getId()),
                bean.getIdentifier(), "instance_metadata");
        checkExecutionStatus(componentInstanceDao.deleteCompInstanceWithInstId(bean.getId()),
                bean.getIdentifier(), "comp_instance");
    }

    /**
     * Adds cluster component instance beans for instances that need cluster deletion.
     * This follows the original logic for handling cluster relationships.
     *
     * @param utilityBean UtilityBean containing the deletion request
     * @return List of cluster ComponentInstanceBean objects to delete
     */
    private List<ComponentInstanceBean> addClusterCompInstanceBeanForInstances(UtilityBean<DeleteInstancePojo> utilityBean) {
        // TODO: Implement cluster logic similar to original - for now return empty list
        // This would require Redis integration to get cluster details
        log.debug("[addClusterCompInstanceBeanForInstances] Cluster logic not yet implemented");
        return new ArrayList<>();
    }

    /**
     * Removes a component instance following the original deletion logic.
     * Handles both hard and soft delete based on instanceRemovalStatus.
     *
     * @param bean ComponentInstanceBean to delete
     * @param utilityBean UtilityBean containing deletion context
     * @throws DataProcessingException if deletion fails
     */
    private void removeComponentInstance(ComponentInstanceBean bean, UtilityBean<DeleteInstancePojo> utilityBean) throws DataProcessingException {
        try {
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            String instanceIdentifier = bean.getIdentifier();
            Boolean instanceRemovalStatus = utilityBean.getPojoObject().getInstanceRemovalStatus();

            log.info("Processing deletion for instanceName:{}, instanceIdentifier:{}, accountIdentifier:{}, instance removal:{}",
                    bean.getName(), instanceIdentifier, accountIdentifier, instanceRemovalStatus);

            if (instanceRemovalStatus != null && instanceRemovalStatus) {
                // Hard delete requested
                hardDeleteInDatabase(bean);
                // TODO: Add OpenSearch deletion logic
                deleteInstanceDetailsFromRedis(accountIdentifier, bean);
            } else if (instanceRemovalStatus == null) {
                // Default behavior based on configuration
                // For now, perform hard delete (can be made configurable)
                hardDeleteInDatabase(bean);
                // TODO: Add OpenSearch deletion logic
                deleteInstanceDetailsFromRedis(accountIdentifier, bean);
            } else {
                // Soft delete requested
                softDeleteInDatabase(bean);
                deleteInstanceDetailsFromRedis(accountIdentifier, bean);
            }

        } catch (Exception e) {
            log.error("Error removing component instance: {}", bean.getIdentifier(), e);
            throw new DataProcessingException("Failed to remove component instance: " + e.getMessage());
        }
    }

    /**
     * Performs hard delete of component instance and all related data in the database.
     *
     * @param bean ComponentInstanceBean to delete
     * @throws HealControlCenterException if deletion fails
     */
    private void hardDeleteInDatabase(ComponentInstanceBean bean) throws HealControlCenterException {
        log.debug("[hardDeleteInDatabase] Deleting component instance: {}", bean.getIdentifier());

        List<Integer> instanceIds = List.of(bean.getId());
        List<String> instanceIdentifiers = List.of(bean.getIdentifier());

        // Delete related data in the correct order (child tables first)
        checkExecutionStatus(componentInstanceDao.deleteComponentInstanceAttributes(instanceIds), instanceIdentifiers, "comp_instance_attribute_values");
        checkExecutionStatus(componentInstanceDao.deleteComponentInstanceKpiThresholds(instanceIds), instanceIdentifiers, "comp_instance_kpi_threshold_details");
        checkExecutionStatus(componentInstanceDao.deleteAgentComponentInstanceMappings(instanceIds), instanceIdentifiers, "agent_comp_instance_mapping");
        checkExecutionStatus(componentInstanceDao.deleteComponentClusterMappings(instanceIds), instanceIdentifiers, "component_cluster_mapping");
        checkExecutionStatus(componentInstanceDao.deleteComponentInstanceTagMappings(instanceIds), instanceIdentifiers, "tag_mapping");
        checkExecutionStatus(componentInstanceDao.deleteInstanceMetadata(instanceIds), instanceIdentifiers, "instance_metadata");

        // Finally delete the component instance itself
        checkExecutionStatus(componentInstanceDao.hardDeleteComponentInstances(instanceIdentifiers, bean.getAccountId()), instanceIdentifiers, "comp_instance");

        log.debug("[hardDeleteInDatabase] Completed hard delete for component instance: {}", bean.getIdentifier());
    }

    /**
     * Performs soft delete of component instance by setting status to 0.
     *
     * @param bean ComponentInstanceBean to soft delete
     * @throws HealControlCenterException if soft delete fails
     */
    private void softDeleteInDatabase(ComponentInstanceBean bean) throws HealControlCenterException {
        log.info("Performing soft delete for instanceName:{}, instanceIdentifier:{}", bean.getName(), bean.getIdentifier());

        // Set status = 0 in comp_instance table only
        int result = componentInstanceDao.softDeleteComponentInstances(List.of(bean.getIdentifier()), bean.getAccountId());
        if (result <= 0) {
            String err = "Exception while inactivating component instance with identifier '" + bean.getIdentifier() + "' in comp_instance table.";
            log.error(err);
            throw new HealControlCenterException(err);
        }
    }

    /**
     * Deletes component instance details from Redis cache.
     * This follows the original logic for Redis cleanup.
     *
     * @param accountIdentifier Account identifier
     * @param componentInstanceBean Component instance to remove from cache
     */
    private void deleteInstanceDetailsFromRedis(String accountIdentifier, ComponentInstanceBean componentInstanceBean) {

        String instanceIdentifier = componentInstanceBean.getIdentifier();

        // Delete instance details key
        instanceRepo.deleteInstanceDetailsForIdentifier(accountIdentifier, instanceIdentifier);

        // Delete instance attributes key
        instanceRepo.deleteAttributeDetails(accountIdentifier, instanceIdentifier);

        //Delete instance metadata key for the instance identifier
        instanceRepo.deleteInstanceMetadataDetailsForIdentifier(accountIdentifier, instanceIdentifier);

        List<CompInstKpiEntity> instanceWiseKpis = instanceRepo.getInstanceWiseKpis(accountIdentifier, instanceIdentifier);
        if (instanceWiseKpis != null) {
            instanceWiseKpis.parallelStream().forEach(kpi -> {
                // Delete kpi level(individual kpi) keys at instance level
                instanceRepo.deleteKpiDetailForIdentifier(accountIdentifier, instanceIdentifier, kpi.getIdentifier());
                instanceRepo.deleteKpiDetailForId(accountIdentifier, instanceIdentifier, kpi.getId());
            });
        }

        // Delete kpis key at instance level
        instanceRepo.deleteKpiDetailForInstance(accountIdentifier, instanceIdentifier);

        //Update account level instance metadata redis key
        List<InstanceMetadata> instanceMetadataList = instanceRepo.getInstanceMetadataByAccountIdentifier(accountIdentifier);

        if (!instanceMetadataList.isEmpty()) {
            Optional<InstanceMetadata> optionalInstanceMetadata = instanceMetadataList
                    .parallelStream()
                    .filter(i -> i.getInstanceIdentifier().equals(instanceIdentifier))
                    .findFirst();
            if (optionalInstanceMetadata.isPresent()) {
                instanceMetadataList.remove(optionalInstanceMetadata.get());
                instanceRepo.updateInstanceMetadataInRedisByAccountIdentifier(accountIdentifier, instanceMetadataList);
            }
        }

        List<CompInstClusterDetails> instanceDetails = instanceRepo.getInstances(accountIdentifier);

        if (!instanceDetails.isEmpty()) {
            CompInstClusterDetails compInstance = instanceDetails.parallelStream().filter(instance -> instance.getIdentifier().equalsIgnoreCase(instanceIdentifier)).findAny().orElse(null);
            if (compInstance != null) {
                instanceDetails.remove(compInstance);
                // Remove instance data at account level instances key
                instanceRepo.updateInstances(accountIdentifier, instanceDetails);
            }
        }

        // Check instance level agents keys
        if (componentInstanceBean.getAgentIdsMap() != null && !componentInstanceBean.getAgentIdsMap().isEmpty()) {
            Map<Integer, String> agentIdsMap = componentInstanceBean.getAgentIdsMap();
            for (int agentId : agentIdsMap.keySet()) {
                List<BasicEntity> existingAgentInstanceMappingDetailsFromRedis = agentRepo.getAgentInstanceMappingDetails(accountIdentifier, componentInstanceBean.getAgentIdsMap().get(agentId));

                if (existingAgentInstanceMappingDetailsFromRedis != null && !existingAgentInstanceMappingDetailsFromRedis.isEmpty()) {
                    BasicEntity agentInstanceMap = existingAgentInstanceMappingDetailsFromRedis.stream().filter(instanceAgentMapDetails -> instanceAgentMapDetails.getIdentifier().equalsIgnoreCase(instanceIdentifier)).findAny().orElse(null);

                    existingAgentInstanceMappingDetailsFromRedis.remove(agentInstanceMap);

                    //Remove instance data at agent level instances key
                    agentRepo.updateAgentInstanceMappingDetails(accountIdentifier, componentInstanceBean.getAgentIdsMap().get(agentId), existingAgentInstanceMappingDetailsFromRedis);
                }
            }
        }
    }

    /**
     * Checks the result of a delete/update operation and logs the result.
     * Note: Unlike applications, component instance related data deletion is not critical for failure,
     * as some related data might not exist for all instances.
     *
     * @param res Result of the operation (number of rows affected).
     * @param instanceIdentifier Component instance identifier.
     * @param tableName Table name where the operation was performed.
     */
    private void checkExecutionStatus(int res, String instanceIdentifier, String tableName) throws HealControlCenterException {
        if (res == -1) {
            log.error("Error occurred while deleting from '[{}]' table for instance with identifier '[{}]'.", tableName, instanceIdentifier);
            throw new HealControlCenterException(String.format("Error occurred while deleting from '[%s]' table for instance with " +
                    "identifier '[%s]'.", tableName, instanceIdentifier));
        } else if (res == 0) {
            log.warn("No Entry found in '[{}]' table for instance with identifier '[{}]'.", tableName, instanceIdentifier);
        } else {
            log.info("[{}] no. of row(s) deleted successfully from table [{}] for instance [{}]", res, tableName, instanceIdentifier);
        }
    }
}
