package com.heal.controlcenter.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
@ManagedResource(objectName = "HealControlCenter:name=ApplicationInfo")
public class HealthMetrics {

    private Map<Integer, Integer> statusCodes = new HashMap<>();

    public void updateHealControlCenterErrors() {
        log.info("Updating Heal controlcenter errors");
    }

    public void updateUnauthorizedRequests() {
        log.info("Updating Unauthorized Requests");
    }

    public void updateResponseTime(String apiName, long time) {
        log.info("Updating response time for API: {} with time: {}ms", apiName, time);
    }

    public void addStatusCodes(Integer statusCode, Integer counter) {
        statusCodes.put(statusCode, statusCodes.getOrDefault(statusCode, 0) + counter);
    }
}