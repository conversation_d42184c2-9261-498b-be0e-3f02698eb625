# Update Instance Details - Spring Boot Migration Analysis

## Executive Summary

This document provides a comprehensive migration analysis for converting the `updateInstanceDetails` API from Spark Java + JDBI to Spring Boot + JDBC architecture. This serves as a reference guide for future migration activities, detailing what components exist in each layer and what database tables and Redis keys are affected.

## Migration Overview

**Original Architecture:** Spark Java Routes → UpdateInstanceBL → JDBI DAO → Redis Repo
**Target Architecture:** Spring Boot Controller → Service (BL) → DAO → Redis Repository

**API Endpoint:** `PUT /api/instances/{instanceId}`

## Spring Boot Architecture Layers

### 1. Controller Layer
**Class:** `UpdateInstanceController`
**Purpose:** HTTP request handling, validation orchestration, response management
**Key Responsibilities:**
- Client validation using Bean Validation annotations
- Server validation coordination
- Request/response mapping
- Exception handling
- HTTP status code management

**Annotations Required:**
- `@RestController` - Marks as REST controller
- `@RequestMapping("/api/instances")` - Base path mapping
- `@PutMapping("/{instanceId}")` - HTTP PUT method mapping
- `@Valid` - Enables automatic validation
- `@PathVariable` - Path parameter binding
- `@RequestBody` - Request body binding

### 2. Service Layer (Business Logic)
**Class:** `UpdateInstanceService`
**Purpose:** Business logic processing, transaction management
**Key Responsibilities:**
- Core business logic execution
- Transaction boundary management
- Database and Redis operation coordination
- Data transformation and mapping
- Business rule enforcement

**Annotations Required:**
- `@Service` - Marks as service component
- `@Transactional` - Transaction management
- `@Autowired` - Dependency injection

**Additional Service Classes:**
- `InstanceValidationService` - Server-side validation logic
- `InstanceMapper` - DTO mapping operations

### 3. Data Access Layer (DAO)
**Class:** `UpdateInstanceDao`
**Purpose:** Database operations using Spring JDBC
**Key Responsibilities:**
- SQL query execution
- Database transaction participation
- Data persistence operations
- Database constraint handling

**Annotations Required:**
- `@Repository` - Marks as data access component
- `@Autowired` - JdbcTemplate injection

**Technology:** Spring JdbcTemplate (replaces JDBI)

### 4. Redis Repository Layer
**Class:** `UpdateInstanceRedisRepository`
**Purpose:** Redis cache operations
**Key Responsibilities:**
- Cache data management
- Redis key pattern management
- Cache synchronization
- Performance optimization

**Annotations Required:**
- `@Repository` - Marks as repository component
- `@Autowired` - RedisTemplate injection

**Technology:** Spring RedisTemplate

## Client Validation (Controller Layer)

### Request DTOs Structure

**Primary Request DTO:** `UpdateInstanceRequest`
**Fields:**
- `instanceIdentifier` (String) - Instance unique identifier
- `name` (String) - Instance display name
- `applications` (List) - Application service mappings
- `environment` (String) - Environment type (PRODUCTION/DR)
- `hostInstance` (boolean) - Host instance flag

**Nested DTOs:**
1. **ApplicationServiceMappingDto**
   **Fields:**
   - `id` (Integer) - Application ID
   - `name` (String) - Application name
   - `identifier` (String) - Application identifier
   - `services` (List) - List of service actions

2. **ServiceActionDto**
   **Fields:**
   - `id` (Integer) - Service ID
   - `name` (String) - Service name
   - `identifier` (String) - Service identifier
   - `action` (String) - Action type (ADD/REMOVE)

### Validation Rules Applied
**Field Validations:**
- Instance identifier: Required, max 128 characters
- Instance name: Required, 1-128 characters
- Application ID: Must be positive integer
- Service ID: Must be positive integer
- Environment: Must match PRODUCTION or DR pattern
- Action: Must be ADD or REMOVE

**Bean Validation Annotations Used:**
- `@Valid` - Enables nested object validation
- `@NotBlank` - String cannot be null or empty
- `@Size` - String length constraints
- `@Positive` - Positive number validation
- `@Pattern` - Regex pattern matching
- `@NotEmpty` - Collection cannot be empty

**Validation Processing:**
- Automatic validation triggered by `@Valid` annotation
- Validation errors automatically converted to HTTP 400 responses
- Custom error messages provided for each validation rule

## Server Validation (Controller Layer)

### Validation Service Component
**Class:** `InstanceValidationService`
**Purpose:** Server-side business validation logic
**Injection:** Injected into Controller via `@Autowired`

### Validation Checks Performed

**1. Instance Existence Validation**
- Verify instance exists in database
- Check instance ID validity
- Validate instance is not deleted/inactive

**2. Account Access Validation**
- Extract account identifier from request headers/JWT
- Verify user has access to the specific instance
- Check account-level permissions

**3. Service Mapping Validation**
- Validate application IDs exist in system
- Verify service IDs are valid
- Check service belongs to specified application
- Validate service-application relationships

**4. Environment Validation**
- Verify environment values (PRODUCTION/DR)
- Check environment change permissions
- Validate environment transition rules

**5. Business Rule Validation**
- Cannot change environment if instance has active monitoring
- Cannot remove all services from production instance
- Validate instance state transitions
- Check dependency constraints

### Validation Flow
**Controller Orchestration:**
1. Automatic client validation via `@Valid`
2. Call `InstanceValidationService.validateUpdateRequest()`
3. Process validation result
4. Return appropriate HTTP response codes

**Validation Result Handling:**
- Valid: Proceed to service layer
- Invalid: Return HTTP 400 with error message
- Access Denied: Return HTTP 403
- Not Found: Return HTTP 404

### Error Response Structure
**Validation Errors:** HTTP 400 with detailed error messages
**Access Errors:** HTTP 403 with access denied message
**Not Found Errors:** HTTP 404 with instance not found
**Server Errors:** HTTP 500 with generic error message

## Processing Phase (Service Layer)

### Service Layer Components

**Primary Service:** `UpdateInstanceService`
**Purpose:** Business logic orchestration and transaction management
**Key Dependencies:**
- `UpdateInstanceDao` - Database operations
- `UpdateInstanceRedisRepository` - Redis cache operations
- `InstanceMapper` - DTO mapping operations

**Transaction Management:**
- Uses Spring `@Transactional` annotation
- Automatic rollback on exceptions
- Database and Redis operations coordinated

### Business Logic Flow

**Main Processing Steps:**
1. **Data Preparation:** Set audit fields (updated_time, user_details_id)
2. **Database Updates:** Execute database operations within transaction
3. **Cache Updates:** Update Redis cache after successful database operations
4. **Response Generation:** Return success/error response

**Database Update Operations:**
- Update instance name (if provided)
- Update instance environment (if provided)
- Update service mappings (if provided)

**Cache Update Operations:**
- Update instance details in Redis
- Update instance mappings and relationships
- Maintain cache consistency

## Database Operations (DAO Layer)

### DAO Component
**Class:** `UpdateInstanceDao`
**Technology:** Spring JdbcTemplate (replaces JDBI)
**Purpose:** Database persistence operations

### Tables Affected

#### 1. `comp_instance` Table
**Operations:** UPDATE
**Fields Updated:**
- `name` - Instance display name
- `is_DR` - Environment flag (0=Production, 1=DR)
- `updated_time` - Last modification timestamp
- `user_details_id` - User who made the change

**SQL Operations:**
- Update instance name query
- Update instance environment query
- WHERE clause: `id = ? AND account_id = ?` (security constraint)

#### 2. `bind_in` Table
**Operations:** DELETE + INSERT (Replace pattern)
**Purpose:** Manage instance-service mappings
**Fields Involved:**
- `instance_id` - Reference to comp_instance
- `service_id` - Reference to service
- `account_id` - Account isolation
- `created_time` - Creation timestamp
- `user_details_id` - User who created mapping

**SQL Operations:**
- Delete existing mappings: `DELETE FROM bind_in WHERE instance_id = ? AND account_id = ?`
- Batch insert new mappings: `INSERT INTO bind_in (...) VALUES (...)`

### DAO Methods Required

**Core Methods:**
- `updateInstanceName(UpdateInstanceDto dto)` - Update instance name
- `updateInstanceEnvironment(UpdateInstanceDto dto)` - Update environment
- `updateServiceMappings(UpdateInstanceDto dto)` - Manage service mappings

**Validation Methods:**
- `instanceExists(Integer instanceId)` - Check instance existence
- `hasInstanceAccess(Integer instanceId, String accountIdentifier)` - Access validation
- `applicationExists(Integer applicationId)` - Application validation
- `serviceExists(Integer serviceId)` - Service validation
- `isValidServiceMapping(Integer appId, List<ServiceActionDto> services)` - Mapping validation

**Query Methods:**
- `getInstanceDetails(Integer instanceId, String accountIdentifier)` - Fetch instance
- `getInstanceEnvironment(Integer instanceId)` - Get current environment
- `hasActiveMonitoring(Integer instanceId)` - Business rule validation

## Redis Operations (Repository Layer)

### Redis Repository Component
**Class:** `UpdateInstanceRedisRepository`
**Technology:** Spring RedisTemplate
**Purpose:** Cache management and performance optimization

### Redis Keys Affected

#### 1. Instance Details Key
**Pattern:** `accounts:{accountIdentifier}:instances:{instanceIdentifier}`
**Data Type:** String (JSON serialized object)
**Content:** Complete instance details (CompInstClusterDetails)
**Operation:** SET (update individual instance)

#### 2. All Instances List Key
**Pattern:** `accounts:{accountIdentifier}:instances`
**Data Type:** List
**Content:** List of all instances for account
**Operation:** List update (replace instance in list)

#### 3. Agent Instance Mapping Keys
**Pattern:** `accounts:{accountIdentifier}:agents:{agentIdentifier}:instances`
**Data Type:** List
**Content:** List of instances mapped to specific agent
**Operation:** List update (update instance details in agent mapping)

#### 4. Service Instance Mapping Keys
**Pattern:** `accounts:{accountIdentifier}:services:{serviceIdentifier}:instances`
**Data Type:** List
**Content:** List of instances mapped to specific service
**Operation:** List update (add/remove instances based on service mappings)

#### 5. Instance Services Relationship Key
**Pattern:** `accounts:{accountIdentifier}:instances:{instanceIdentifier}:services`
**Data Type:** List
**Content:** List of services mapped to specific instance
**Operation:** List replacement (update service relationships)

### Redis Repository Methods

**Core Update Methods:**
- `updateInstanceDetails(UpdateInstanceDto dto)` - Update main instance data
- `updateInstanceMappings(UpdateInstanceDto dto)` - Update all relationship mappings
- `updateAgentInstanceMappings(UpdateInstanceDto dto)` - Update agent relationships
- `updateServiceInstanceMappings(UpdateInstanceDto dto)` - Update service relationships
- `updateInstanceServiceRelationships(UpdateInstanceDto dto)` - Update bidirectional service links

**Helper Methods:**
- `buildInstanceKey(String accountIdentifier, String instanceIdentifier)` - Key construction
- `buildAllInstancesKey(String accountIdentifier)` - List key construction
- `updateInstanceInList(String listKey, CompInstClusterDetails instance)` - List item update
- `buildInstanceDetails(UpdateInstanceDto dto)` - DTO to Redis entity conversion

**Query Methods:**
- `getInstanceDetails(Integer instanceId, String accountIdentifier)` - Fetch from cache
- `cacheInstanceDetails(CompInstClusterDetails instance)` - Cache population

### Cache Update Strategy
**Update Order:**
1. Update individual instance details
2. Update instance in all instances list
3. Update agent-instance mappings
4. Update service-instance mappings
5. Update instance-service relationships

**Error Handling:**
- Redis failures don't rollback database transaction
- Cache inconsistency logged but not fatal
- Cache can be rebuilt from database if needed

**Performance Considerations:**
- Batch Redis operations where possible
- Use Redis pipelines for multiple operations
- JSON serialization for complex objects
- TTL management for cache expiration

## DTOs and Entities Structure

### Service Layer DTOs

#### Primary DTO: UpdateInstanceDto
**Purpose:** Internal service layer data transfer
**Fields:**
- `instanceId` (Integer) - Database primary key
- `instanceIdentifier` (String) - Business identifier
- `name` (String) - Display name
- `environment` (String) - Environment type
- `isDR` (Integer) - DR flag (0=Production, 1=DR)
- `hostInstance` (boolean) - Host instance indicator
- `accountIdentifier` (String) - Account business identifier
- `accountId` (Integer) - Account database ID
- `userDetailsId` (String) - Audit user identifier
- `updatedTime` (LocalDateTime) - Modification timestamp
- `createdTime` (LocalDateTime) - Creation timestamp
- `serviceMappings` (List<ServiceMappingDto>) - Service relationships

#### Supporting DTO: ServiceMappingDto
**Purpose:** Service mapping operations
**Fields:**
- `serviceId` (Integer) - Service database ID
- `serviceName` (String) - Service display name
- `serviceIdentifier` (String) - Service business identifier
- `action` (String) - Operation type (ADD/REMOVE)

### Redis Cache Entities

#### CompInstClusterDetails
**Purpose:** Complete instance information for Redis cache
**Fields:**
- `id` (Integer) - Instance ID
- `name` (String) - Instance name
- `identifier` (String) - Instance identifier
- `componentId` (Integer) - Component reference
- `componentName` (String) - Component name
- `componentTypeId` (Integer) - Component type reference
- `componentTypeName` (String) - Component type name
- `hostId` (Integer) - Host reference
- `hostName` (String) - Host name
- `hostIdentifier` (String) - Host identifier
- `agentIds` (List<String>) - Associated agent identifiers
- `status` (Integer) - Instance status
- `isDR` (Integer) - Environment flag
- `createdTime` (LocalDateTime) - Creation timestamp
- `updatedTime` (LocalDateTime) - Last update timestamp

#### BasicEntity
**Purpose:** Generic ID-Name-Identifier mapping for Redis
**Fields:**
- `id` (Integer) - Entity ID
- `name` (String) - Entity name
- `identifier` (String) - Entity identifier
- `createdTime` (LocalDateTime) - Creation timestamp
- `updatedTime` (LocalDateTime) - Update timestamp

#### BasicInstanceBean
**Purpose:** Service-instance mapping information
**Fields:**
- `id` (Integer) - Instance ID
- `name` (String) - Instance name
- `identifier` (String) - Instance identifier
- `status` (Integer) - Instance status
- `createdTime` (LocalDateTime) - Creation timestamp
- `updatedTime` (LocalDateTime) - Update timestamp

### Database Entity Mapping

#### comp_instance Table Fields
**Primary Fields:**
- `id` - Primary key (Integer)
- `name` - Instance name (VARCHAR 128)
- `identifier` - Business identifier (VARCHAR 128)
- `is_DR` - Environment flag (TINYINT)
- `account_id` - Account reference (Integer)
- `updated_time` - Modification timestamp (DATETIME)
- `user_details_id` - Audit user (VARCHAR 256)

#### bind_in Table Fields
**Primary Fields:**
- `id` - Primary key (Integer)
- `instance_id` - Instance reference (Integer)
- `service_id` - Service reference (Integer)
- `account_id` - Account reference (Integer)
- `created_time` - Creation timestamp (DATETIME)
- `user_details_id` - Audit user (VARCHAR 256)

### Mapping Components

#### InstanceMapper
**Purpose:** Convert between different DTO/Entity types
**Key Methods:**
- `mapToDto(instanceId, request, httpRequest)` - Request to service DTO
- `mapToRedisEntity(dto)` - Service DTO to Redis entity
- `mapToBasicEntity(dto)` - Service DTO to basic entity
- `extractAccountInfo(httpRequest)` - Extract account details from request

## Migration Data Flow Summary

### Request Processing Flow
```
HTTP PUT /api/instances/{instanceId}
    ↓
UpdateInstanceController.updateInstanceDetails()
    ├── Client Validation (@Valid annotations)
    ├── Server Validation (InstanceValidationService)
    └── Process (UpdateInstanceService)
        ↓
UpdateInstanceService.updateInstance() → @Transactional
    ├── Database Updates (UpdateInstanceDao)
    │   ├── comp_instance table (name, is_DR fields)
    │   └── bind_in table (service mappings)
    └── Redis Updates (UpdateInstanceRedisRepository)
        ├── Instance details cache
        ├── Agent mappings cache
        ├── Service mappings cache
        └── Instance-service relationships cache
```

### Component Interaction Flow
```
Controller Layer
    ├── UpdateInstanceController (HTTP handling)
    ├── InstanceValidationService (Server validation)
    └── InstanceMapper (DTO mapping)
        ↓
Service Layer
    └── UpdateInstanceService (Business logic + Transaction)
        ↓
Data Access Layer
    ├── UpdateInstanceDao (Database operations)
    └── UpdateInstanceRedisRepository (Cache operations)
        ↓
Data Storage Layer
    ├── MySQL Database (comp_instance, bind_in tables)
    └── Redis Cache (Instance details, mappings)
```

## Migration Architecture Summary

### Layer Responsibilities

**Controller Layer:**
- HTTP request/response handling
- Client validation using Bean Validation
- Server validation coordination
- Exception handling and HTTP status management
- Request/response DTO mapping

**Service Layer:**
- Business logic execution
- Transaction boundary management
- Database and cache operation coordination
- Data transformation and validation
- Error handling and logging

**DAO Layer:**
- Database operations using JdbcTemplate
- SQL query execution
- Transaction participation
- Data persistence and retrieval

**Repository Layer:**
- Redis cache operations using RedisTemplate
- Cache key management
- Cache synchronization
- Performance optimization

### Technology Migration Map

**Original → Target:**
- Spark Java Routes → Spring Boot @RestController
- JDBI → Spring JdbcTemplate
- Manual validation → Bean Validation (@Valid)
- Manual transaction → @Transactional
- Custom Redis operations → RedisTemplate
- Manual dependency injection → @Autowired

### Database Impact Summary

**Tables Modified:**
- `comp_instance` - Instance name and environment updates
- `bind_in` - Service mapping management

**Redis Keys Modified:**
- Instance details cache
- All instances list cache
- Agent-instance mapping cache
- Service-instance mapping cache
- Instance-service relationship cache

### Migration Benefits

**Development Benefits:**
- Automatic validation and error handling
- Declarative transaction management
- Dependency injection and IoC
- Built-in security features
- Comprehensive testing support

**Operational Benefits:**
- Better monitoring and metrics
- Health checks and actuator endpoints
- Configuration management
- Production-ready features
- Scalability improvements

## Spring Boot Migration Features

### 1. Transaction Management
**Feature:** Declarative transaction management with @Transactional
**Benefits:**
- Automatic transaction boundaries
- Rollback on exceptions
- Connection pooling and resource management
- No manual transaction handling required

### 2. Bean Validation
**Feature:** Automatic validation using annotations
**Benefits:**
- Client-side validation with @Valid
- Custom validation messages
- Nested object validation
- Reduced boilerplate validation code

### 3. Dependency Injection
**Feature:** Automatic dependency injection with @Autowired
**Benefits:**
- No manual object creation
- Singleton management
- Circular dependency detection
- Easy testing with mocking

### 4. Error Handling
**Feature:** Global exception handling with @ControllerAdvice
**Benefits:**
- Centralized error handling
- Consistent error responses
- Automatic HTTP status code mapping
- Custom exception handling

### 5. Security Integration
**Feature:** Built-in security with Spring Security
**Benefits:**
- Method-level security
- JWT token validation
- Role-based access control
- CSRF protection

### 6. Monitoring and Metrics
**Feature:** Spring Boot Actuator integration
**Benefits:**
- Health checks
- Metrics collection
- Application monitoring
- Performance insights

### 7. Configuration Management
**Feature:** Externalized configuration with @ConfigurationProperties
**Benefits:**
- Environment-specific configurations
- Type-safe configuration binding
- Configuration validation
- Hot reloading capabilities

## Migration Checklist

### Pre-Migration Requirements

**Dependencies to Add:**
- spring-boot-starter-web
- spring-boot-starter-data-jdbc
- spring-boot-starter-data-redis
- spring-boot-starter-validation
- spring-boot-starter-security
- spring-boot-starter-actuator

**Configuration Files:**
- application.yml (database and Redis configuration)
- Security configuration
- Logging configuration
- Cache configuration

### Migration Steps

**Step 1: Controller Layer Migration**
- Create UpdateInstanceController with @RestController
- Add request mapping annotations
- Implement client validation with @Valid
- Add server validation service integration
- Implement exception handling

**Step 2: Service Layer Migration**
- Create UpdateInstanceService with @Service
- Add @Transactional annotation
- Implement business logic methods
- Add dependency injection with @Autowired

**Step 3: DAO Layer Migration**
- Create UpdateInstanceDao with @Repository
- Replace JDBI with JdbcTemplate
- Convert SQL queries to Spring JDBC format
- Implement batch operations for performance

**Step 4: Redis Repository Migration**
- Create UpdateInstanceRedisRepository with @Repository
- Replace custom Redis operations with RedisTemplate
- Implement cache key management
- Add error handling for cache operations

**Step 5: DTO and Entity Migration**
- Create request/response DTOs
- Add validation annotations
- Create service layer DTOs
- Implement mapping between DTOs

### Testing Strategy

**Unit Testing:**
- Controller layer testing with MockMvc
- Service layer testing with @MockBean
- DAO layer testing with @DataJdbcTest
- Redis repository testing with @DataRedisTest

**Integration Testing:**
- Full API testing with @SpringBootTest
- Database integration testing
- Redis integration testing
- Security integration testing

### Performance Considerations

**Database Optimizations:**
- Connection pooling with HikariCP
- Prepared statement caching
- Batch operations for multiple updates
- Query optimization

**Redis Optimizations:**
- Connection pooling with Lettuce
- Pipeline operations for multiple commands
- JSON serialization optimization
- TTL management for cache expiration

**Application Optimizations:**
- Spring Cache abstraction
- Lazy loading where appropriate
- Async processing for non-critical operations
- Monitoring and metrics collection

### Security Considerations

**Authentication & Authorization:**
- JWT token validation
- Role-based access control
- Method-level security
- Account-level data isolation

**Input Validation:**
- Bean validation for all inputs
- SQL injection prevention
- XSS protection
- Data sanitization

### Monitoring and Observability

**Metrics Collection:**
- Spring Boot Actuator endpoints
- Custom metrics for business operations
- Database connection pool metrics
- Redis cache hit/miss ratios

**Logging:**
- Structured logging with correlation IDs
- Performance logging for slow operations
- Error logging with stack traces
- Audit logging for data changes

### Rollback Strategy

**Database Changes:**
- Maintain backward compatibility
- Use feature flags for gradual rollout
- Database migration scripts
- Data backup and restore procedures

**Application Deployment:**
- Blue-green deployment strategy
- Canary releases for testing
- Health checks for deployment validation
- Automated rollback triggers

This migration analysis provides a comprehensive reference for converting the update instance API from JDBI to Spring Boot JDBC while maintaining all existing functionality and improving maintainability, performance, and observability.
